# 📄 MQL4 模組化 EA 系統 – 專案設計規格文件（草稿）

---

## 🧭 一、專案目標

建立一套具擴展性、模組化、易於使用與維護的 MQL4 程式框架，透過程式模組的集中管理與自動註冊機制，協助開發者以高效率方式撰寫交易邏輯，減少繁瑣的 `OnInit`、`OnTick`、`OnDeinit` 編排與流程管理。

---

## 📌 二、核心設計理念

1. **模組化**：所有邏輯功能皆封裝為模組類別（繼承自 `ProgramBase`）。
2. **集中式管理**：由 `ProgramManager` 負責註冊與執行模組。
3. **自動註冊**：模組在建構時自動向管理器註冊，不需外部手動登記。
4. **輕量開發**：開發者僅需專注於每個模組的 `Execute()` 內容。
5. **易於擴展**：可快速加入新模組，不需改動核心架構。

---

## 🧩 三、模組功能與需求分析

### A. 程序管理（ProgramManager）

| 功能     | 說明                                                        |
| -------- | ----------------------------------------------------------- |
| 模組註冊 | 根據 `ProgramType` 將模組分類註冊至 INIT、TICK、DEINIT 清單 |
| 執行控制 | 提供 `RunInit()`、`RunTick()`、`RunDeinit()` 執行相對應模組 |
| 清單結構 | 使用 `CArrayObj` 管理不同階段模組陣列                       |

---

### B. 模組基類（ProgramBase）

| 功能         | 說明                                                    |
| ------------ | ------------------------------------------------------- |
| 虛擬基類     | 所有模組繼承，統一介面（`Execute()`）                   |
| 執行階段定義 | 使用 `ProgramType` 設定模組所屬階段                     |
| 自動註冊     | 在建構函數中主動向 ProgramManager 註冊自己              |
| 成員屬性     | 名稱（`name`）、類型（`type`）、管理器指標（`manager`） |

---

### C. 核心函數管理器（MainController）

| 功能                     | 說明                                               |
| ------------------------ | -------------------------------------------------- |
| 對接 EA 核心函數         | 封裝 `OnInit()`、`OnTick()`、`OnDeinit()` 執行流程 |
| 持有 ProgramManager 實例 | 轉交控制權至管理器                                 |
| 啟動模組註冊             | 在初始化階段可建立必要模組（策略、日誌等）         |

---

### D. 程序類型（ProgramType 枚舉）

```mql4
enum ProgramType {
    PROGRAM_TYPE_INIT,
    PROGRAM_TYPE_TICK,
    PROGRAM_TYPE_DEINIT
};
```

## 🧱 四、類別與模組結構圖（文字 UML）

```mermaid
classDiagram
    class ProgramBase {
        <<abstract>>
        - name: string
        - type: ProgramType
        - manager: ProgramManager*
        + Execute(): void
        + GetType(): ProgramType
        + constructor(...)
    }

    class CustomProgramA {
        <<user-defined>>
        + Execute(): void
    }

    class ProgramManager {
        - initPrograms: CArrayObj
        - tickPrograms: CArrayObj
        - deinitPrograms: CArrayObj
        + Register(p: ProgramBase*)
        + RunInit()
        + RunTick()
        + RunDeinit()
    }

    class MainController {
        - manager: ProgramManager
        + OnInit()
        + OnTick()
        + OnDeinit()
    }

    class ProgramType {
        <<enumeration>>
        PROGRAM_TYPE_INIT
        PROGRAM_TYPE_TICK
        PROGRAM_TYPE_DEINIT
    }

    ProgramBase <|-- CustomProgramA
    ProgramBase "1" o-- "0..*" ProgramManager : manager
    ProgramManager "1" o-- "0..*" ProgramBase : manages
    MainController "1" o-- "1" ProgramManager : manager
    ProgramBase --> ProgramType : type
```

## 🧪 五、範例使用方式

```mql4
// 建立主控制器
MainController controller;

int OnInit()    { return controller.OnInit(); }
void OnTick()   { controller.OnTick(); }
void OnDeinit(const int reason) { controller.OnDeinit(); }

// CustomProgramA 在建構時自動註冊至 manager
```

---

## 🚧 六、開發階段規劃（建議）

| 階段         | 工作項目                                            |
| ------------ | --------------------------------------------------- |
| ✅ 第 1 階段 | 設計 `ProgramBase`, `ProgramManager` 與自動註冊機制 |
| ✅ 第 2 階段 | 建立 `MainController` 並整合 EA 函數流程            |
| 🔜 第 3 階段 | 開發使用者模組（如 TickLogger, TradeStrategy）範例  |
| 🔜 第 4 階段 | 引入設定讀取、日誌輸出、錯誤處理模組                |
| 🔜 第 5 階段 | 單元測試、範本建立、自動化工具支持                  |

---

---

## 📎 七、附註

所有模組請遵循單一職責原則

模組命名統一以 ProgramXXX 為開頭

程式碼請區分 .mqh 為類別定義，.mq4 為主控制器執行邏輯

```yaml
如需我幫你產出 PDF 檔、建立 GitHub 專案結構、補上實作程式碼範本，隨時告訴我！
```

---
