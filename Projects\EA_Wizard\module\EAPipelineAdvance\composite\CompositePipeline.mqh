//+------------------------------------------------------------------+
//|                                           CompositePipeline.mqh |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../core/TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 複合流水線類                                                     |
//+------------------------------------------------------------------+
class CompositePipeline : public TradingPipeline
{
private:
    ITradingPipeline* m_children[];     // 子流水線數組
    int m_maxChildren;                  // 最大子流水線數量
    ENUM_EXECUTION_MODE m_executionMode; // 執行模式
    bool m_stopOnError;                 // 遇到錯誤時是否停止
    int m_currentIndex;                 // 當前執行索引

public:
    // 構造函數
    CompositePipeline(string name, 
                     int maxChildren = DEFAULT_MAX_PIPELINES,
                     ENUM_EXECUTION_MODE executionMode = EXECUTION_SEQUENTIAL,
                     bool stopOnError = true)
        : TradingPipeline(name, COMPOSITE_PIPELINE_TYPE),
          m_maxChildren(maxChildren),
          m_executionMode(executionMode),
          m_stopOnError(stopOnError),
          m_currentIndex(0)
    {
        ArrayResize(m_children, 0);
    }
    
    // 析構函數
    virtual ~CompositePipeline()
    {
        Clear();
    }
    
    // 添加子流水線
    bool Add(ITradingPipeline* child)
    {
        if(child == NULL)
        {
            SetResult(false, "子流水線不能為空", ERROR_LEVEL_ERROR);
            return false;
        }
        
        if(ArraySize(m_children) >= m_maxChildren)
        {
            SetResult(false, "已達到最大子流水線數量限制", ERROR_LEVEL_ERROR);
            return false;
        }
        
        // 檢查是否已存在
        for(int i = 0; i < ArraySize(m_children); i++)
        {
            if(m_children[i] == child)
            {
                SetResult(false, "子流水線已存在", ERROR_LEVEL_WARNING);
                return false;
            }
        }
        
        // 添加到數組
        int newSize = ArraySize(m_children) + 1;
        ArrayResize(m_children, newSize);
        m_children[newSize - 1] = child;
        
        LogMessage("添加子流水線: " + child.GetName());
        return true;
    }
    
    // 移除子流水線
    bool Remove(ITradingPipeline* child)
    {
        if(child == NULL)
        {
            SetResult(false, "子流水線不能為空", ERROR_LEVEL_ERROR);
            return false;
        }
        
        int index = -1;
        for(int i = 0; i < ArraySize(m_children); i++)
        {
            if(m_children[i] == child)
            {
                index = i;
                break;
            }
        }
        
        if(index == -1)
        {
            SetResult(false, "子流水線不存在", ERROR_LEVEL_WARNING);
            return false;
        }
        
        // 移除元素
        for(int i = index; i < ArraySize(m_children) - 1; i++)
        {
            m_children[i] = m_children[i + 1];
        }
        ArrayResize(m_children, ArraySize(m_children) - 1);
        
        LogMessage("移除子流水線: " + child.GetName());
        return true;
    }
    
    // 按名稱移除子流水線
    bool RemoveByName(string name)
    {
        for(int i = 0; i < ArraySize(m_children); i++)
        {
            if(m_children[i].GetName() == name)
            {
                return Remove(m_children[i]);
            }
        }
        
        SetResult(false, "未找到名稱為 '" + name + "' 的子流水線", ERROR_LEVEL_WARNING);
        return false;
    }
    
    // 清空所有子流水線
    void Clear()
    {
        ArrayResize(m_children, 0);
        m_currentIndex = 0;
        LogMessage("清空所有子流水線");
    }
    
    // 獲取子流水線數量
    int GetChildCount() const { return ArraySize(m_children); }
    
    // 獲取最大子流水線數量
    int GetMaxChildren() const { return m_maxChildren; }
    
    // 按索引獲取子流水線
    ITradingPipeline* GetChild(int index)
    {
        if(index < 0 || index >= ArraySize(m_children))
        {
            return NULL;
        }
        return m_children[index];
    }
    
    // 按名稱查找子流水線
    ITradingPipeline* FindByName(string name)
    {
        for(int i = 0; i < ArraySize(m_children); i++)
        {
            if(m_children[i].GetName() == name)
            {
                return m_children[i];
            }
        }
        return NULL;
    }
    
    // 重置所有子流水線
    virtual void Reset() override
    {
        TradingPipeline::Reset();
        
        for(int i = 0; i < ArraySize(m_children); i++)
        {
            m_children[i].Reset();
        }
        
        m_currentIndex = 0;
        LogMessage("重置複合流水線及所有子流水線");
    }
    
    // 設置執行模式
    void SetExecutionMode(ENUM_EXECUTION_MODE mode) { m_executionMode = mode; }
    ENUM_EXECUTION_MODE GetExecutionMode() const { return m_executionMode; }
    
    // 設置錯誤處理策略
    void SetStopOnError(bool stopOnError) { m_stopOnError = stopOnError; }
    bool GetStopOnError() const { return m_stopOnError; }
    
    // 獲取執行進度
    double GetProgress() const
    {
        int totalChildren = ArraySize(m_children);
        if(totalChildren == 0) return 100.0;
        
        return (double)m_currentIndex / totalChildren * 100.0;
    }
    
    // 獲取當前執行索引
    int GetCurrentIndex() const { return m_currentIndex; }

protected:
    // 實現具體的執行邏輯
    virtual bool DoExecute() override
    {
        if(ArraySize(m_children) == 0)
        {
            SetResult(true, "沒有子流水線需要執行");
            return true;
        }
        
        LogMessage(StringFormat("開始執行複合流水線，包含 %d 個子流水線", ArraySize(m_children)));
        
        bool overallSuccess = true;
        int successCount = 0;
        int failureCount = 0;
        
        m_currentIndex = 0;
        
        for(int i = 0; i < ArraySize(m_children); i++)
        {
            m_currentIndex = i;
            ITradingPipeline* child = m_children[i];
            
            LogMessage(StringFormat("執行子流水線 [%d/%d]: %s", 
                                  i + 1, ArraySize(m_children), child.GetName()));
            
            bool success = child.Execute();
            
            if(success)
            {
                successCount++;
                LogMessage("子流水線執行成功: " + child.GetName());
            }
            else
            {
                failureCount++;
                overallSuccess = false;
                
                string errorMsg = StringFormat("子流水線執行失敗: %s, 錯誤: %s", 
                                             child.GetName(), child.GetLastError());
                LogMessage(errorMsg, ERROR_LEVEL_ERROR);
                
                if(m_stopOnError)
                {
                    LogMessage("遇到錯誤停止執行", ERROR_LEVEL_WARNING);
                    break;
                }
            }
        }
        
        m_currentIndex = ArraySize(m_children); // 標記為完成
        
        string resultMsg = StringFormat("複合流水線執行完成: 成功=%d, 失敗=%d, 總計=%d", 
                                       successCount, failureCount, successCount + failureCount);
        
        if(overallSuccess)
        {
            SetResult(true, resultMsg);
            LogMessage(resultMsg);
        }
        else
        {
            SetResult(false, resultMsg, ERROR_LEVEL_ERROR);
            LogMessage(resultMsg, ERROR_LEVEL_ERROR);
        }
        
        return overallSuccess;
    }
};
