# EAPipelineAdvance 使用示例

## 概述

本文檔提供了 EAPipelineAdvance 模組的詳細使用示例，展示如何使用重構後的簡化架構來構建高效的交易流水線系統。

## 基本使用

### 1. 創建簡單流水線

```mql4
#include "EAPipelineAdvance.mqh"

// 創建自定義流水線
class MyDataFeedPipeline : public TradingPipeline
{
public:
    MyDataFeedPipeline() : TradingPipeline("DataFeed", "MyDataFeedPipeline") {}

protected:
    virtual bool DoExecute() override
    {
        LogMessage("開始獲取市場數據");
        
        // 實際的數據獲取邏輯
        double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
        double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
        
        if(bid > 0 && ask > 0)
        {
            SetResult(true, StringFormat("數據獲取成功: Bid=%.5f, Ask=%.5f", bid, ask));
            return true;
        }
        else
        {
            SetResult(false, "無法獲取市場數據", ERROR_LEVEL_ERROR);
            return false;
        }
    }
};

void OnStart()
{
    // 創建並執行流水線
    MyDataFeedPipeline* pipeline = new MyDataFeedPipeline();
    
    bool success = pipeline.Execute();
    if(success)
    {
        Print("流水線執行成功: ", pipeline.GetLastResult().GetMessage());
    }
    else
    {
        Print("流水線執行失敗: ", pipeline.GetLastError());
    }
    
    delete pipeline;
}
```

### 2. 使用複合流水線

```mql4
void OnStart()
{
    // 創建複合流水線
    CompositePipeline* mainPipeline = new CompositePipeline("MainTradingPipeline");
    
    // 添加子流水線
    mainPipeline.Add(new MyDataFeedPipeline());
    mainPipeline.Add(new MySignalPipeline());
    mainPipeline.Add(new MyOrderPipeline());
    
    // 執行所有子流水線
    bool success = mainPipeline.Execute();
    
    Print("主流水線執行結果: ", success ? "成功" : "失敗");
    Print("執行進度: ", mainPipeline.GetProgress(), "%");
    
    delete mainPipeline;
}
```

### 3. 使用流水線執行器

```mql4
void OnStart()
{
    // 創建執行器
    PipelineExecutor* executor = new PipelineExecutor("MainExecutor", EXECUTION_SEQUENTIAL, true, 3);
    
    // 添加多個流水線
    executor.AddPipeline(new MyDataFeedPipeline());
    executor.AddPipeline(new MySignalPipeline());
    executor.AddPipeline(new MyRiskPipeline());
    
    // 執行所有流水線
    bool success = executor.ExecuteAll();
    
    // 獲取執行統計
    ExecutionStats* stats = executor.GetStats();
    Print("執行統計: ", stats.ToString());
    
    delete executor;
}
```

## 高級使用

### 1. 使用日誌功能

```mql4
void OnStart()
{
    // 創建基礎流水線
    MyDataFeedPipeline* basePipeline = new MyDataFeedPipeline();
    
    // 創建日誌配置
    LogConfig config(true, true, "trading_pipeline.log", ERROR_LEVEL_INFO, true, true);
    
    // 包裝為日誌流水線
    LoggingPipeline* loggingPipeline = new LoggingPipeline(basePipeline, config);
    
    // 執行（會自動記錄日誌）
    loggingPipeline.Execute();
    
    delete loggingPipeline;
    delete basePipeline;
}
```

### 2. 使用註冊器管理流水線

```mql4
void OnStart()
{
    // 創建註冊器
    TradingPipelineRegistry* registry = new TradingPipelineRegistry("MainRegistry");
    
    // 註冊流水線
    MyDataFeedPipeline* dataFeed = new MyDataFeedPipeline();
    MySignalPipeline* signal = new MySignalPipeline();
    
    registry.RegisterPipeline(dataFeed);
    registry.RegisterPipeline(signal);
    
    // 按名稱執行流水線
    bool success1 = registry.ExecutePipeline("DataFeed");
    bool success2 = registry.ExecutePipeline("Signal");
    
    Print("註冊器狀態: ", registry.GetStatusInfo());
    
    delete registry;
    delete dataFeed;
    delete signal;
}
```

### 3. 使用流水線組

```mql4
void OnStart()
{
    // 創建不同事件的流水線組
    PipelineGroup* initGroup = new PipelineGroup("InitGroup", "初始化流水線組", TRADING_INIT);
    PipelineGroup* tickGroup = new PipelineGroup("TickGroup", "交易流水線組", TRADING_TICK);
    
    // 創建複合流水線並添加到組
    CompositePipeline* initPipeline = new CompositePipeline("InitComposite");
    initPipeline.Add(new MyInitPipeline());
    initGroup.AddPipeline(initPipeline);
    
    CompositePipeline* tickPipeline = new CompositePipeline("TickComposite");
    tickPipeline.Add(new MyDataFeedPipeline());
    tickPipeline.Add(new MySignalPipeline());
    tickGroup.AddPipeline(tickPipeline);
    
    // 執行不同組
    Print("執行初始化組...");
    initGroup.ExecuteAll();
    
    Print("執行交易組...");
    tickGroup.ExecuteAll();
    
    delete initGroup;
    delete tickGroup;
}
```

## 完整的 EA 示例

### 使用模組管理器的完整 EA

```mql4
#include "EAPipelineAdvance.mqh"

// 自定義流水線實現
class EAInitPipeline : public TradingPipeline
{
public:
    EAInitPipeline() : TradingPipeline("EAInit", "EAInitPipeline") {}

protected:
    virtual bool DoExecute() override
    {
        LogMessage("EA 初始化開始");
        
        // 檢查交易環境
        if(!IsTradeAllowed())
        {
            SetResult(false, "交易不被允許", ERROR_LEVEL_ERROR);
            return false;
        }
        
        // 初始化指標
        // ... 初始化邏輯
        
        SetResult(true, "EA 初始化完成");
        return true;
    }
};

class EATickPipeline : public TradingPipeline
{
public:
    EATickPipeline() : TradingPipeline("EATick", "EATickPipeline") {}

protected:
    virtual bool DoExecute() override
    {
        LogMessage("處理新的價格變動");
        
        // 獲取市場數據
        double price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
        
        // 分析信號
        // ... 信號分析邏輯
        
        // 執行交易
        // ... 交易邏輯
        
        SetResult(true, "Tick 處理完成");
        return true;
    }
};

// EA 事件處理函數
int OnInit()
{
    // 初始化模組
    if(!InitializeEAPipelineAdvance())
    {
        Print("EAPipelineAdvance 初始化失敗");
        return INIT_FAILED;
    }
    
    // 註冊初始化流水線
    EAInitPipeline* initPipeline = new EAInitPipeline();
    if(!RegisterTradingPipeline(initPipeline, TRADING_INIT))
    {
        Print("註冊初始化流水線失敗");
        return INIT_FAILED;
    }
    
    // 註冊交易流水線
    EATickPipeline* tickPipeline = new EATickPipeline();
    if(!RegisterTradingPipeline(tickPipeline, TRADING_TICK))
    {
        Print("註冊交易流水線失敗");
        return INIT_FAILED;
    }
    
    // 執行初始化事件
    if(!ExecuteTradingEvent(TRADING_INIT))
    {
        Print("執行初始化事件失敗");
        return INIT_FAILED;
    }
    
    Print("EA 初始化成功");
    PrintEAPipelineAdvanceInfo();
    
    return INIT_SUCCEEDED;
}

void OnTick()
{
    // 執行交易事件
    ExecuteTradingEvent(TRADING_TICK);
}

void OnDeinit(const int reason)
{
    // 執行清理事件
    ExecuteTradingEvent(TRADING_DEINIT);
    
    // 釋放模組資源
    EAPipelineAdvanceManager::ReleaseInstance();
    
    Print("EA 清理完成");
}
```

## 錯誤處理示例

### 1. 基本錯誤處理

```mql4
void OnStart()
{
    MyDataFeedPipeline* pipeline = new MyDataFeedPipeline();
    
    bool success = pipeline.Execute();
    if(!success)
    {
        Print("流水線執行失敗:");
        Print("錯誤信息: ", pipeline.GetLastError());
        Print("狀態: ", TradingEventUtils::StatusToString(pipeline.GetStatus()));
        
        if(pipeline.GetLastResult() != NULL)
        {
            Print("詳細結果: ", pipeline.GetLastResult().ToString());
        }
    }
    
    delete pipeline;
}
```

### 2. 複合流水線錯誤處理

```mql4
void OnStart()
{
    // 創建不停止於錯誤的複合流水線
    CompositePipeline* pipeline = new CompositePipeline("ErrorTolerantPipeline", 10, EXECUTION_SEQUENTIAL, false);
    
    pipeline.Add(new MyDataFeedPipeline());
    pipeline.Add(new MyFailingPipeline());  // 這個會失敗
    pipeline.Add(new MySignalPipeline());   // 但這個仍會執行
    
    bool success = pipeline.Execute();
    
    Print("整體執行結果: ", success ? "成功" : "部分失敗");
    Print("執行進度: ", pipeline.GetProgress(), "%");
    
    // 檢查各個子流水線的狀態
    for(int i = 0; i < pipeline.GetChildCount(); i++)
    {
        ITradingPipeline* child = pipeline.GetChild(i);
        if(child != NULL)
        {
            Print("子流水線 [", i, "] ", child.GetName(), ": ", 
                  child.HasError() ? "失敗" : "成功");
        }
    }
    
    delete pipeline;
}
```

## 性能優化建議

### 1. 重用流水線對象

```mql4
// 全局變量
MyDataFeedPipeline* g_dataFeedPipeline = NULL;

int OnInit()
{
    // 創建一次，重複使用
    g_dataFeedPipeline = new MyDataFeedPipeline();
    return INIT_SUCCEEDED;
}

void OnTick()
{
    // 重置並重新執行
    g_dataFeedPipeline.Reset();
    g_dataFeedPipeline.Execute();
}

void OnDeinit(const int reason)
{
    if(g_dataFeedPipeline != NULL)
    {
        delete g_dataFeedPipeline;
        g_dataFeedPipeline = NULL;
    }
}
```

### 2. 條件執行

```mql4
class ConditionalPipeline : public TradingPipeline
{
private:
    bool m_condition;

public:
    ConditionalPipeline(string name) : TradingPipeline(name), m_condition(false) {}
    
    void SetCondition(bool condition) { m_condition = condition; }

protected:
    virtual bool DoExecute() override
    {
        if(!m_condition)
        {
            SetResult(true, "條件不滿足，跳過執行");
            return true;
        }
        
        // 實際執行邏輯
        return DoActualWork();
    }
    
    virtual bool DoActualWork() = 0;
};
```

## 最佳實踐

1. **使用有意義的名稱**：為流水線和組件使用清晰、描述性的名稱
2. **適當的錯誤處理**：根據業務需求決定是否在錯誤時停止執行
3. **日誌記錄**：使用日誌功能記錄重要的執行信息
4. **資源管理**：及時釋放不再使用的對象
5. **模組化設計**：將相關功能組織到適當的流水線中
6. **測試驗證**：使用測試框架驗證流水線的正確性

## 遷移指南

從原始 EAPipeline 模組遷移到 EAPipelineAdvance：

1. **更新 include 語句**：
   ```mql4
   // 舊版本
   #include "EAPipeline/EAPipeline.mqh"
   
   // 新版本
   #include "EAPipelineAdvance/EAPipelineAdvance.mqh"
   ```

2. **更新類名**：
   - `EAPipeline` → `TradingPipeline`
   - `EAPipelineManager` → `PipelineExecutor`
   - `EACompoundPipeline` → `CompositePipeline`

3. **簡化初始化**：
   ```mql4
   // 舊版本（複雜的裝飾者鏈）
   EARegistryBase* registry = new EALogRegistry(new EAErrorHandlingRegistry(...));
   
   // 新版本（簡化的管理器）
   InitializeEAPipelineAdvance();
   ```

4. **使用新的便利函數**：
   ```mql4
   // 新版本提供的便利函數
   RegisterTradingPipeline(pipeline, TRADING_TICK);
   ExecuteTradingEvent(TRADING_TICK);
   ```
