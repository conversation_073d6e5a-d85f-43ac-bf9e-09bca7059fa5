//+------------------------------------------------------------------+
//|                                    TestEAPipelineAdvanceDemo.mq4 |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EAPipelineAdvance"
#property link      ""
#property version   "1.00"
#property strict

#include "EAPipelineAdvance.mqh"
#include "test/TestEAPipelineAdvance.mqh"

//+------------------------------------------------------------------+
//| 腳本程序開始函數                                                 |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== EAPipelineAdvance 模組演示 ===");
    
    // 1. 基本功能演示
    DemoBasicFunctionality();
    
    // 2. 高級功能演示
    DemoAdvancedFeatures();
    
    // 3. 運行完整測試套件
    Print("\n=== 運行測試套件 ===");
    RunEAPipelineAdvanceTests();
    
    // 4. 模組信息
    Print("\n=== 模組信息 ===");
    PrintEAPipelineAdvanceInfo();
    
    Print("\n=== 演示完成 ===");
}

//+------------------------------------------------------------------+
//| 基本功能演示                                                     |
//+------------------------------------------------------------------+
void DemoBasicFunctionality()
{
    Print("\n--- 基本功能演示 ---");
    
    // 1. 創建簡單流水線
    Print("1. 創建和執行簡單流水線");
    SimpleTradingPipeline* simplePipeline = new SimpleTradingPipeline("DemoSimple", "演示簡單任務");
    
    bool success = simplePipeline.Execute();
    Print("執行結果: ", success ? "成功" : "失敗");
    Print("狀態: ", TradingEventUtils::StatusToString(simplePipeline.GetStatus()));
    
    delete simplePipeline;
    
    // 2. 創建複合流水線
    Print("\n2. 創建和執行複合流水線");
    CompositePipeline* composite = new CompositePipeline("DemoComposite");
    
    composite.Add(new SimpleTradingPipeline("Task1", "任務1"));
    composite.Add(new SimpleTradingPipeline("Task2", "任務2"));
    composite.Add(new SimpleTradingPipeline("Task3", "任務3"));
    
    Print("子流水線數量: ", composite.GetChildCount());
    
    success = composite.Execute();
    Print("複合流水線執行結果: ", success ? "成功" : "失敗");
    Print("執行進度: ", composite.GetProgress(), "%");
    
    delete composite;
    
    // 3. 使用執行器
    Print("\n3. 使用流水線執行器");
    PipelineExecutor* executor = new PipelineExecutor("DemoExecutor");
    
    executor.AddPipeline(new SimpleTradingPipeline("ExecTask1", "執行器任務1"));
    executor.AddPipeline(new SimpleTradingPipeline("ExecTask2", "執行器任務2"));
    
    success = executor.ExecuteAll();
    Print("執行器執行結果: ", success ? "成功" : "失敗");
    
    ExecutionStats* stats = executor.GetStats();
    Print("執行統計: ", stats.ToString());
    
    delete executor;
}

//+------------------------------------------------------------------+
//| 高級功能演示                                                     |
//+------------------------------------------------------------------+
void DemoAdvancedFeatures()
{
    Print("\n--- 高級功能演示 ---");
    
    // 1. 日誌功能
    Print("1. 日誌功能演示");
    SimpleTradingPipeline* basePipeline = new SimpleTradingPipeline("LogDemo", "日誌演示任務");
    
    // 創建日誌配置
    LogConfig logConfig(true, false, "demo.log", ERROR_LEVEL_INFO, true, true);
    LoggingPipeline* loggingPipeline = new LoggingPipeline(basePipeline, logConfig, "LoggingDemo");
    
    bool success = loggingPipeline.Execute();
    Print("日誌流水線執行結果: ", success ? "成功" : "失敗");
    
    delete loggingPipeline;
    delete basePipeline;
    
    // 2. 註冊器功能
    Print("\n2. 註冊器功能演示");
    TradingPipelineRegistry* registry = new TradingPipelineRegistry("DemoRegistry");
    
    SimpleTradingPipeline* regPipeline1 = new SimpleTradingPipeline("RegTask1", "註冊器任務1");
    SimpleTradingPipeline* regPipeline2 = new SimpleTradingPipeline("RegTask2", "註冊器任務2");
    
    RegistryResult* result1 = registry.RegisterPipeline(regPipeline1);
    RegistryResult* result2 = registry.RegisterPipeline(regPipeline2);
    
    Print("註冊結果1: ", result1.ToString());
    Print("註冊結果2: ", result2.ToString());
    
    delete result1;
    delete result2;
    
    Print("註冊器狀態: ", registry.GetStatusInfo());
    
    // 按名稱執行流水線
    bool execSuccess1 = registry.ExecutePipeline("RegTask1");
    bool execSuccess2 = registry.ExecutePipeline("RegTask2");
    
    Print("註冊器執行結果: Task1=", execSuccess1 ? "成功" : "失敗", 
          ", Task2=", execSuccess2 ? "成功" : "失敗");
    
    delete registry;
    delete regPipeline1;
    delete regPipeline2;
    
    // 3. 流水線組功能
    Print("\n3. 流水線組功能演示");
    PipelineGroup* group = new PipelineGroup("DemoGroup", "演示組", TRADING_TICK);
    
    CompositePipeline* groupComposite1 = new CompositePipeline("GroupComposite1");
    groupComposite1.Add(new SimpleTradingPipeline("GroupTask1", "組任務1"));
    
    CompositePipeline* groupComposite2 = new CompositePipeline("GroupComposite2");
    groupComposite2.Add(new SimpleTradingPipeline("GroupTask2", "組任務2"));
    
    group.AddPipeline(groupComposite1);
    group.AddPipeline(groupComposite2);
    
    Print("組狀態: ", group.GetStatusInfo());
    
    success = group.ExecuteAll();
    Print("組執行結果: ", success ? "成功" : "失敗");
    
    delete group;
    
    // 4. 模組管理器功能
    Print("\n4. 模組管理器功能演示");
    
    // 初始化模組
    bool initialized = InitializeEAPipelineAdvance();
    Print("模組初始化: ", initialized ? "成功" : "失敗");
    
    if(initialized)
    {
        // 註冊流水線到不同事件
        SimpleTradingPipeline* initPipeline = new SimpleTradingPipeline("InitDemo", "初始化演示");
        SimpleTradingPipeline* tickPipeline = new SimpleTradingPipeline("TickDemo", "Tick演示");
        
        bool regInit = RegisterTradingPipeline(initPipeline, TRADING_INIT);
        bool regTick = RegisterTradingPipeline(tickPipeline, TRADING_TICK);
        
        Print("註冊到INIT事件: ", regInit ? "成功" : "失敗");
        Print("註冊到TICK事件: ", regTick ? "成功" : "失敗");
        
        // 執行事件
        bool execInit = ExecuteTradingEvent(TRADING_INIT);
        bool execTick = ExecuteTradingEvent(TRADING_TICK);
        
        Print("執行INIT事件: ", execInit ? "成功" : "失敗");
        Print("執行TICK事件: ", execTick ? "成功" : "失敗");
        
        delete initPipeline;
        delete tickPipeline;
        
        // 釋放管理器
        EAPipelineAdvanceManager::ReleaseInstance();
    }
}

//+------------------------------------------------------------------+
//| 錯誤處理演示                                                     |
//+------------------------------------------------------------------+
class DemoFailingPipeline : public TradingPipeline
{
public:
    DemoFailingPipeline() : TradingPipeline("FailingDemo", "DemoFailingPipeline") {}

protected:
    virtual bool DoExecute() override
    {
        LogMessage("這是一個會失敗的演示流水線", ERROR_LEVEL_WARNING);
        SetResult(false, "演示失敗情況", ERROR_LEVEL_ERROR);
        return false;
    }
};

//+------------------------------------------------------------------+
//| 錯誤處理演示函數                                                 |
//+------------------------------------------------------------------+
void DemoErrorHandling()
{
    Print("\n--- 錯誤處理演示 ---");
    
    // 創建會失敗的流水線
    DemoFailingPipeline* failingPipeline = new DemoFailingPipeline();
    
    bool success = failingPipeline.Execute();
    Print("失敗流水線執行結果: ", success ? "成功" : "失敗");
    Print("錯誤信息: ", failingPipeline.GetLastError());
    Print("狀態: ", TradingEventUtils::StatusToString(failingPipeline.GetStatus()));
    
    // 在複合流水線中測試錯誤處理
    CompositePipeline* errorTestComposite = new CompositePipeline("ErrorTest", 10, EXECUTION_SEQUENTIAL, false);
    
    errorTestComposite.Add(new SimpleTradingPipeline("BeforeError", "錯誤前的任務"));
    errorTestComposite.Add(failingPipeline);
    errorTestComposite.Add(new SimpleTradingPipeline("AfterError", "錯誤後的任務"));
    
    success = errorTestComposite.Execute();
    Print("包含錯誤的複合流水線執行結果: ", success ? "成功" : "部分失敗");
    
    delete errorTestComposite;
    delete failingPipeline;
}
