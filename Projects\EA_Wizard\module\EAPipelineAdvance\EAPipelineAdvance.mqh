//+------------------------------------------------------------------+
//|                                           EAPipelineAdvance.mqh |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

//+------------------------------------------------------------------+
//| EAPipelineAdvance 模組主入口文件                                 |
//| 這是對原始 EAPipeline 模組的重構版本，專注於簡化架構和提高可讀性 |
//+------------------------------------------------------------------+

// 核心組件
#include "core/TradingEvent.mqh"
#include "core/TradingPipeline.mqh"
#include "core/PipelineExecutor.mqh"

// 組合組件
#include "composite/CompositePipeline.mqh"
#include "composite/PipelineGroup.mqh"

// 功能組件
#include "features/LoggingPipeline.mqh"
#include "features/PipelineRegistry.mqh"

//+------------------------------------------------------------------+
//| 模組版本信息                                                     |
//+------------------------------------------------------------------+
const string EAPIPELINE_ADVANCE_VERSION = "1.0.0";
const string EAPIPELINE_ADVANCE_BUILD_DATE = "2024-01-01";
const string EAPIPELINE_ADVANCE_DESCRIPTION = "EAPipeline 模組重構版本 - 簡化架構，提高可讀性";

//+------------------------------------------------------------------+
//| 模組工廠類                                                       |
//+------------------------------------------------------------------+
class EAPipelineAdvanceFactory
{
public:
    // 創建簡單交易流水線
    static SimpleTradingPipeline* CreateSimplePipeline(string name, string task)
    {
        return new SimpleTradingPipeline(name, task);
    }
    
    // 創建複合流水線
    static CompositePipeline* CreateCompositePipeline(string name, 
                                                     int maxChildren = DEFAULT_MAX_PIPELINES,
                                                     ENUM_EXECUTION_MODE mode = EXECUTION_SEQUENTIAL,
                                                     bool stopOnError = true)
    {
        return new CompositePipeline(name, maxChildren, mode, stopOnError);
    }
    
    // 創建帶日誌的流水線
    static LoggingPipeline* CreateLoggingPipeline(ITradingPipeline* pipeline, 
                                                  bool enableFile = false,
                                                  string logFileName = "pipeline.log")
    {
        LogConfig config(true, enableFile, logFileName);
        return new LoggingPipeline(pipeline, config);
    }
    
    // 創建流水線執行器
    static PipelineExecutor* CreateExecutor(string name = "PipelineExecutor",
                                           ENUM_EXECUTION_MODE mode = EXECUTION_SEQUENTIAL,
                                           bool stopOnError = true,
                                           int maxRetries = DEFAULT_MAX_RETRIES)
    {
        return new PipelineExecutor(name, mode, stopOnError, maxRetries);
    }
    
    // 創建流水線組
    static PipelineGroup* CreatePipelineGroup(string name,
                                             string description = "",
                                             ENUM_TRADING_EVENT eventType = TRADING_TICK)
    {
        return new PipelineGroup(name, description, eventType);
    }
    
    // 創建流水線註冊器
    static TradingPipelineRegistry* CreateRegistry(string name = "TradingPipelineRegistry",
                                                  int maxItems = DEFAULT_MAX_PIPELINES)
    {
        return new TradingPipelineRegistry(name, maxItems);
    }
};

//+------------------------------------------------------------------+
//| 模組管理器                                                       |
//+------------------------------------------------------------------+
class EAPipelineAdvanceManager
{
private:
    static EAPipelineAdvanceManager* s_instance;
    TradingPipelineRegistry* m_registry;
    PipelineExecutor* m_executor;
    PipelineGroup* m_initGroup;
    PipelineGroup* m_tickGroup;
    PipelineGroup* m_deinitGroup;
    bool m_initialized;

public:
    // 獲取單例實例
    static EAPipelineAdvanceManager* GetInstance()
    {
        if(s_instance == NULL)
        {
            s_instance = new EAPipelineAdvanceManager();
        }
        return s_instance;
    }
    
    // 釋放單例實例
    static void ReleaseInstance()
    {
        if(s_instance != NULL)
        {
            delete s_instance;
            s_instance = NULL;
        }
    }
    
    // 初始化管理器
    bool Initialize()
    {
        if(m_initialized)
        {
            return true;
        }
        
        Print("初始化 EAPipelineAdvance 管理器...");
        
        // 創建註冊器
        m_registry = EAPipelineAdvanceFactory::CreateRegistry();
        
        // 創建執行器
        m_executor = EAPipelineAdvanceFactory::CreateExecutor("MainExecutor");
        
        // 創建事件組
        m_initGroup = EAPipelineAdvanceFactory::CreatePipelineGroup("InitGroup", "初始化流水線組", TRADING_INIT);
        m_tickGroup = EAPipelineAdvanceFactory::CreatePipelineGroup("TickGroup", "交易流水線組", TRADING_TICK);
        m_deinitGroup = EAPipelineAdvanceFactory::CreatePipelineGroup("DeinitGroup", "清理流水線組", TRADING_DEINIT);
        
        m_initialized = true;
        Print("EAPipelineAdvance 管理器初始化完成");
        
        return true;
    }
    
    // 註冊流水線到指定事件組
    bool RegisterPipeline(ITradingPipeline* pipeline, ENUM_TRADING_EVENT eventType)
    {
        if(!m_initialized)
        {
            Print("管理器未初始化");
            return false;
        }
        
        if(pipeline == NULL)
        {
            Print("流水線不能為空");
            return false;
        }
        
        // 註冊到註冊器
        RegistryResult* result = m_registry.RegisterPipeline(pipeline);
        if(!result.IsSuccess())
        {
            Print("註冊流水線失敗: ", result.GetMessage());
            delete result;
            return false;
        }
        delete result;
        
        // 添加到對應的事件組
        CompositePipeline* composite = new CompositePipeline(pipeline.GetName() + "_Composite");
        composite.Add(pipeline);
        
        bool added = false;
        switch(eventType)
        {
            case TRADING_INIT:
                added = m_initGroup.AddPipeline(composite);
                break;
            case TRADING_TICK:
                added = m_tickGroup.AddPipeline(composite);
                break;
            case TRADING_DEINIT:
                added = m_deinitGroup.AddPipeline(composite);
                break;
        }
        
        if(added)
        {
            Print("流水線 [", pipeline.GetName(), "] 已註冊到 ", TradingEventUtils::EventToString(eventType), " 事件組");
        }
        
        return added;
    }
    
    // 執行指定事件的所有流水線
    bool ExecuteEvent(ENUM_TRADING_EVENT eventType)
    {
        if(!m_initialized)
        {
            Print("管理器未初始化");
            return false;
        }
        
        PipelineGroup* group = GetEventGroup(eventType);
        if(group == NULL)
        {
            Print("未找到事件組: ", TradingEventUtils::EventToString(eventType));
            return false;
        }
        
        Print("執行 ", TradingEventUtils::EventToString(eventType), " 事件流水線...");
        return group.ExecuteAll();
    }
    
    // 獲取註冊器
    TradingPipelineRegistry* GetRegistry() { return m_registry; }
    
    // 獲取執行器
    PipelineExecutor* GetExecutor() { return m_executor; }
    
    // 獲取事件組
    PipelineGroup* GetEventGroup(ENUM_TRADING_EVENT eventType)
    {
        switch(eventType)
        {
            case TRADING_INIT:   return m_initGroup;
            case TRADING_TICK:   return m_tickGroup;
            case TRADING_DEINIT: return m_deinitGroup;
            default:             return NULL;
        }
    }
    
    // 獲取模組信息
    string GetModuleInfo()
    {
        return StringFormat("EAPipelineAdvance v%s (%s) - %s",
                          EAPIPELINE_ADVANCE_VERSION,
                          EAPIPELINE_ADVANCE_BUILD_DATE,
                          EAPIPELINE_ADVANCE_DESCRIPTION);
    }
    
    // 獲取統計信息
    string GetStatistics()
    {
        if(!m_initialized)
        {
            return "管理器未初始化";
        }
        
        return StringFormat("統計信息:\n%s\nInit組: %d個流水線\nTick組: %d個流水線\nDeinit組: %d個流水線",
                          m_registry.GetStatusInfo(),
                          m_initGroup.GetPipelineCount(),
                          m_tickGroup.GetPipelineCount(),
                          m_deinitGroup.GetPipelineCount());
    }

private:
    // 私有構造函數
    EAPipelineAdvanceManager()
        : m_registry(NULL),
          m_executor(NULL),
          m_initGroup(NULL),
          m_tickGroup(NULL),
          m_deinitGroup(NULL),
          m_initialized(false)
    {
    }
    
    // 析構函數
    ~EAPipelineAdvanceManager()
    {
        if(m_registry != NULL) delete m_registry;
        if(m_executor != NULL) delete m_executor;
        if(m_initGroup != NULL) delete m_initGroup;
        if(m_tickGroup != NULL) delete m_tickGroup;
        if(m_deinitGroup != NULL) delete m_deinitGroup;
    }
};

// 初始化靜態成員
EAPipelineAdvanceManager* EAPipelineAdvanceManager::s_instance = NULL;

//+------------------------------------------------------------------+
//| 便利函數                                                         |
//+------------------------------------------------------------------+

// 快速初始化模組
bool InitializeEAPipelineAdvance()
{
    return EAPipelineAdvanceManager::GetInstance().Initialize();
}

// 快速註冊流水線
bool RegisterTradingPipeline(ITradingPipeline* pipeline, ENUM_TRADING_EVENT eventType)
{
    return EAPipelineAdvanceManager::GetInstance().RegisterPipeline(pipeline, eventType);
}

// 快速執行事件
bool ExecuteTradingEvent(ENUM_TRADING_EVENT eventType)
{
    return EAPipelineAdvanceManager::GetInstance().ExecuteEvent(eventType);
}

// 獲取模組版本
string GetEAPipelineAdvanceVersion()
{
    return EAPIPELINE_ADVANCE_VERSION;
}

// 打印模組信息
void PrintEAPipelineAdvanceInfo()
{
    Print("=== EAPipelineAdvance 模組信息 ===");
    Print(EAPipelineAdvanceManager::GetInstance().GetModuleInfo());
    Print(EAPipelineAdvanceManager::GetInstance().GetStatistics());
    Print("================================");
}
