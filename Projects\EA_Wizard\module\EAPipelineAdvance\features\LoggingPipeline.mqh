//+------------------------------------------------------------------+
//|                                            LoggingPipeline.mqh |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../core/TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 日誌配置                                                         |
//+------------------------------------------------------------------+
class LogConfig
{
private:
    bool m_enableConsole;       // 是否輸出到控制台
    bool m_enableFile;          // 是否輸出到文件
    string m_logFileName;       // 日誌文件名
    ENUM_ERROR_LEVEL m_minLevel; // 最小日誌級別
    bool m_includeTimestamp;    // 是否包含時間戳
    bool m_includePipelineName; // 是否包含流水線名稱

public:
    // 構造函數
    LogConfig(bool enableConsole = true,
             bool enableFile = false,
             string logFileName = "pipeline.log",
             ENUM_ERROR_LEVEL minLevel = ERROR_LEVEL_INFO,
             bool includeTimestamp = true,
             bool includePipelineName = true)
        : m_enableConsole(enableConsole),
          m_enableFile(enableFile),
          m_logFileName(logFileName),
          m_minLevel(minLevel),
          m_includeTimestamp(includeTimestamp),
          m_includePipelineName(includePipelineName)
    {
    }
    
    // 獲取配置
    bool IsConsoleEnabled() const { return m_enableConsole; }
    bool IsFileEnabled() const { return m_enableFile; }
    string GetLogFileName() const { return m_logFileName; }
    ENUM_ERROR_LEVEL GetMinLevel() const { return m_minLevel; }
    bool IsTimestampIncluded() const { return m_includeTimestamp; }
    bool IsPipelineNameIncluded() const { return m_includePipelineName; }
    
    // 設置配置
    void SetConsoleEnabled(bool enabled) { m_enableConsole = enabled; }
    void SetFileEnabled(bool enabled) { m_enableFile = enabled; }
    void SetLogFileName(string fileName) { m_logFileName = fileName; }
    void SetMinLevel(ENUM_ERROR_LEVEL level) { m_minLevel = level; }
    void SetTimestampIncluded(bool included) { m_includeTimestamp = included; }
    void SetPipelineNameIncluded(bool included) { m_includePipelineName = included; }
};

//+------------------------------------------------------------------+
//| 日誌記錄器                                                       |
//+------------------------------------------------------------------+
class Logger
{
private:
    LogConfig m_config;         // 日誌配置
    int m_fileHandle;           // 文件句柄
    bool m_fileOpened;          // 文件是否已打開

public:
    // 構造函數
    Logger(LogConfig config)
        : m_config(config),
          m_fileHandle(-1),
          m_fileOpened(false)
    {
        if(m_config.IsFileEnabled())
        {
            OpenLogFile();
        }
    }
    
    // 析構函數
    ~Logger()
    {
        CloseLogFile();
    }
    
    // 記錄日誌
    void Log(ENUM_ERROR_LEVEL level, string message, string pipelineName = "")
    {
        // 檢查日誌級別
        if(level < m_config.GetMinLevel())
        {
            return;
        }
        
        string formattedMessage = FormatMessage(level, message, pipelineName);
        
        // 輸出到控制台
        if(m_config.IsConsoleEnabled())
        {
            Print(formattedMessage);
        }
        
        // 輸出到文件
        if(m_config.IsFileEnabled() && m_fileOpened)
        {
            FileWrite(m_fileHandle, formattedMessage);
            FileFlush(m_fileHandle);
        }
    }
    
    // 記錄信息
    void Info(string message, string pipelineName = "")
    {
        Log(ERROR_LEVEL_INFO, message, pipelineName);
    }
    
    // 記錄警告
    void Warning(string message, string pipelineName = "")
    {
        Log(ERROR_LEVEL_WARNING, message, pipelineName);
    }
    
    // 記錄錯誤
    void Error(string message, string pipelineName = "")
    {
        Log(ERROR_LEVEL_ERROR, message, pipelineName);
    }
    
    // 記錄嚴重錯誤
    void Critical(string message, string pipelineName = "")
    {
        Log(ERROR_LEVEL_CRITICAL, message, pipelineName);
    }
    
    // 獲取配置
    LogConfig* GetConfig() { return &m_config; }

private:
    // 格式化消息
    string FormatMessage(ENUM_ERROR_LEVEL level, string message, string pipelineName)
    {
        string result = "";
        
        // 添加時間戳
        if(m_config.IsTimestampIncluded())
        {
            result += "[" + TimeToString(TimeCurrent(), TIME_DATE | TIME_SECONDS) + "] ";
        }
        
        // 添加級別
        result += GetLevelString(level) + " ";
        
        // 添加流水線名稱
        if(m_config.IsPipelineNameIncluded() && pipelineName != "")
        {
            result += "[" + pipelineName + "] ";
        }
        
        // 添加消息
        result += message;
        
        return result;
    }
    
    // 獲取級別字符串
    string GetLevelString(ENUM_ERROR_LEVEL level)
    {
        switch(level)
        {
            case ERROR_LEVEL_INFO:     return "[INFO]";
            case ERROR_LEVEL_WARNING:  return "[WARN]";
            case ERROR_LEVEL_ERROR:    return "[ERROR]";
            case ERROR_LEVEL_CRITICAL: return "[CRITICAL]";
            default:                   return "[UNKNOWN]";
        }
    }
    
    // 打開日誌文件
    void OpenLogFile()
    {
        m_fileHandle = FileOpen(m_config.GetLogFileName(), FILE_WRITE | FILE_TXT);
        if(m_fileHandle != INVALID_HANDLE)
        {
            m_fileOpened = true;
            FileSeek(m_fileHandle, 0, SEEK_END); // 移動到文件末尾
        }
        else
        {
            Print("無法打開日誌文件: ", m_config.GetLogFileName());
        }
    }
    
    // 關閉日誌文件
    void CloseLogFile()
    {
        if(m_fileOpened && m_fileHandle != INVALID_HANDLE)
        {
            FileClose(m_fileHandle);
            m_fileOpened = false;
            m_fileHandle = -1;
        }
    }
};

//+------------------------------------------------------------------+
//| 帶日誌功能的流水線                                               |
//+------------------------------------------------------------------+
class LoggingPipeline : public TradingPipeline
{
private:
    ITradingPipeline* m_wrappedPipeline; // 被包裝的流水線
    Logger* m_logger;                    // 日誌記錄器
    bool m_ownLogger;                    // 是否擁有日誌記錄器

public:
    // 構造函數 - 使用默認日誌配置
    LoggingPipeline(ITradingPipeline* pipeline, string name = "")
        : TradingPipeline(name != "" ? name : ("Logging_" + pipeline.GetName()), LOGGING_PIPELINE_TYPE),
          m_wrappedPipeline(pipeline),
          m_ownLogger(true)
    {
        LogConfig config;
        m_logger = new Logger(config);
    }
    
    // 構造函數 - 使用自定義日誌配置
    LoggingPipeline(ITradingPipeline* pipeline, LogConfig config, string name = "")
        : TradingPipeline(name != "" ? name : ("Logging_" + pipeline.GetName()), LOGGING_PIPELINE_TYPE),
          m_wrappedPipeline(pipeline),
          m_ownLogger(true)
    {
        m_logger = new Logger(config);
    }
    
    // 構造函數 - 使用外部日誌記錄器
    LoggingPipeline(ITradingPipeline* pipeline, Logger* logger, string name = "")
        : TradingPipeline(name != "" ? name : ("Logging_" + pipeline.GetName()), LOGGING_PIPELINE_TYPE),
          m_wrappedPipeline(pipeline),
          m_logger(logger),
          m_ownLogger(false)
    {
    }
    
    // 析構函數
    virtual ~LoggingPipeline()
    {
        if(m_ownLogger && m_logger != NULL)
        {
            delete m_logger;
        }
    }
    
    // 獲取被包裝的流水線
    ITradingPipeline* GetWrappedPipeline() { return m_wrappedPipeline; }
    
    // 獲取日誌記錄器
    Logger* GetLogger() { return m_logger; }

protected:
    // 實現具體的執行邏輯
    virtual bool DoExecute() override
    {
        if(m_wrappedPipeline == NULL)
        {
            m_logger.Error("被包裝的流水線為空", GetName());
            SetResult(false, "被包裝的流水線為空", ERROR_LEVEL_ERROR);
            return false;
        }
        
        m_logger.Info("開始執行流水線: " + m_wrappedPipeline.GetName(), GetName());
        
        bool success = m_wrappedPipeline.Execute();
        
        if(success)
        {
            m_logger.Info("流水線執行成功: " + m_wrappedPipeline.GetName(), GetName());
            SetResult(true, "流水線執行成功");
        }
        else
        {
            string errorMsg = "流水線執行失敗: " + m_wrappedPipeline.GetLastError();
            m_logger.Error(errorMsg, GetName());
            SetResult(false, errorMsg, ERROR_LEVEL_ERROR);
        }
        
        return success;
    }
    
    // 重寫日誌方法
    virtual void LogMessage(string message, ENUM_ERROR_LEVEL level = ERROR_LEVEL_INFO) override
    {
        if(m_logger != NULL)
        {
            m_logger.Log(level, message, GetName());
        }
        else
        {
            // 回退到基類實現
            TradingPipeline::LogMessage(message, level);
        }
    }
};
