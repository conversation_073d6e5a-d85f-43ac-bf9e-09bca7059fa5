//+------------------------------------------------------------------+
//|                                              PipelineGroup.mqh |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "CompositePipeline.mqh"

//+------------------------------------------------------------------+
//| 流水線組類                                                       |
//+------------------------------------------------------------------+
class PipelineGroup
{
private:
    string m_name;                      // 組名稱
    string m_description;               // 組描述
    CompositePipeline* m_pipelines[];   // 流水線數組
    bool m_isEnabled;                   // 是否啟用
    ENUM_TRADING_EVENT m_eventType;     // 關聯的事件類型

public:
    // 構造函數
    PipelineGroup(string name, 
                 string description = "",
                 ENUM_TRADING_EVENT eventType = TRADING_TICK)
        : m_name(name),
          m_description(description),
          m_isEnabled(true),
          m_eventType(eventType)
    {
        ArrayResize(m_pipelines, 0);
    }
    
    // 析構函數
    ~PipelineGroup()
    {
        Clear();
    }
    
    // 添加流水線
    bool AddPipeline(CompositePipeline* pipeline)
    {
        if(pipeline == NULL)
        {
            Print("錯誤: 流水線不能為空");
            return false;
        }
        
        // 檢查是否已存在
        for(int i = 0; i < ArraySize(m_pipelines); i++)
        {
            if(m_pipelines[i] == pipeline)
            {
                Print("警告: 流水線已存在於組中");
                return false;
            }
        }
        
        // 添加到數組
        int newSize = ArraySize(m_pipelines) + 1;
        ArrayResize(m_pipelines, newSize);
        m_pipelines[newSize - 1] = pipeline;
        
        Print("添加流水線到組 [", m_name, "]: ", pipeline.GetName());
        return true;
    }
    
    // 移除流水線
    bool RemovePipeline(CompositePipeline* pipeline)
    {
        if(pipeline == NULL)
        {
            Print("錯誤: 流水線不能為空");
            return false;
        }
        
        int index = -1;
        for(int i = 0; i < ArraySize(m_pipelines); i++)
        {
            if(m_pipelines[i] == pipeline)
            {
                index = i;
                break;
            }
        }
        
        if(index == -1)
        {
            Print("警告: 流水線不存在於組中");
            return false;
        }
        
        // 移除元素
        for(int i = index; i < ArraySize(m_pipelines) - 1; i++)
        {
            m_pipelines[i] = m_pipelines[i + 1];
        }
        ArrayResize(m_pipelines, ArraySize(m_pipelines) - 1);
        
        Print("從組 [", m_name, "] 移除流水線: ", pipeline.GetName());
        return true;
    }
    
    // 按名稱查找流水線
    CompositePipeline* FindPipelineByName(string name)
    {
        for(int i = 0; i < ArraySize(m_pipelines); i++)
        {
            if(m_pipelines[i].GetName() == name)
            {
                return m_pipelines[i];
            }
        }
        return NULL;
    }
    
    // 執行組中所有流水線
    bool ExecuteAll()
    {
        if(!m_isEnabled)
        {
            Print("流水線組 [", m_name, "] 已禁用，跳過執行");
            return true;
        }
        
        if(ArraySize(m_pipelines) == 0)
        {
            Print("流水線組 [", m_name, "] 沒有流水線可執行");
            return true;
        }
        
        Print("開始執行流水線組 [", m_name, "]，包含 ", ArraySize(m_pipelines), " 個流水線");
        
        bool overallSuccess = true;
        int successCount = 0;
        int failureCount = 0;
        
        for(int i = 0; i < ArraySize(m_pipelines); i++)
        {
            CompositePipeline* pipeline = m_pipelines[i];
            
            Print("執行流水線 [", i + 1, "/", ArraySize(m_pipelines), "]: ", pipeline.GetName());
            
            bool success = pipeline.Execute();
            
            if(success)
            {
                successCount++;
                Print("流水線執行成功: ", pipeline.GetName());
            }
            else
            {
                failureCount++;
                overallSuccess = false;
                Print("流水線執行失敗: ", pipeline.GetName(), ", 錯誤: ", pipeline.GetLastError());
            }
        }
        
        Print("流水線組 [", m_name, "] 執行完成: 成功=", successCount, ", 失敗=", failureCount);
        
        return overallSuccess;
    }
    
    // 重置組中所有流水線
    void ResetAll()
    {
        for(int i = 0; i < ArraySize(m_pipelines); i++)
        {
            m_pipelines[i].Reset();
        }
        Print("重置流水線組 [", m_name, "] 中的所有流水線");
    }
    
    // 清空組
    void Clear()
    {
        ArrayResize(m_pipelines, 0);
        Print("清空流水線組 [", m_name, "]");
    }
    
    // 獲取流水線數量
    int GetPipelineCount() const { return ArraySize(m_pipelines); }
    
    // 獲取組名稱
    string GetName() const { return m_name; }
    
    // 獲取組描述
    string GetDescription() const { return m_description; }
    
    // 設置組描述
    void SetDescription(string description) { m_description = description; }
    
    // 啟用/禁用組
    void SetEnabled(bool enabled) 
    { 
        m_isEnabled = enabled; 
        Print("流水線組 [", m_name, "] ", enabled ? "已啟用" : "已禁用");
    }
    bool IsEnabled() const { return m_isEnabled; }
    
    // 獲取/設置事件類型
    ENUM_TRADING_EVENT GetEventType() const { return m_eventType; }
    void SetEventType(ENUM_TRADING_EVENT eventType) { m_eventType = eventType; }
    
    // 獲取組狀態信息
    string GetStatusInfo() const
    {
        int totalPipelines = ArraySize(m_pipelines);
        int completedPipelines = 0;
        int failedPipelines = 0;
        
        for(int i = 0; i < totalPipelines; i++)
        {
            if(m_pipelines[i].IsCompleted())
            {
                completedPipelines++;
            }
            else if(m_pipelines[i].HasError())
            {
                failedPipelines++;
            }
        }
        
        return StringFormat("組 [%s]: 總數=%d, 完成=%d, 失敗=%d, 狀態=%s",
                          m_name, totalPipelines, completedPipelines, failedPipelines,
                          m_isEnabled ? "啟用" : "禁用");
    }
    
    // 按索引獲取流水線
    CompositePipeline* GetPipeline(int index)
    {
        if(index < 0 || index >= ArraySize(m_pipelines))
        {
            return NULL;
        }
        return m_pipelines[index];
    }
    
    // 獲取所有流水線的名稱
    void GetPipelineNames(string &names[])
    {
        ArrayResize(names, ArraySize(m_pipelines));
        for(int i = 0; i < ArraySize(m_pipelines); i++)
        {
            names[i] = m_pipelines[i].GetName();
        }
    }
    
    // 檢查是否包含指定流水線
    bool ContainsPipeline(CompositePipeline* pipeline)
    {
        if(pipeline == NULL) return false;
        
        for(int i = 0; i < ArraySize(m_pipelines); i++)
        {
            if(m_pipelines[i] == pipeline)
            {
                return true;
            }
        }
        return false;
    }
    
    // 檢查是否包含指定名稱的流水線
    bool ContainsPipelineByName(string name)
    {
        return FindPipelineByName(name) != NULL;
    }
};
