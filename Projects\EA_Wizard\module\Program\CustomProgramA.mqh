//+------------------------------------------------------------------+
//|                                              CustomProgramA.mqh |
//|                                    EA_Wizard 模組化程序系統      |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "ProgramBase.mqh"
#include "ProgramManager.mqh"

//+------------------------------------------------------------------+
//| 自定義程序 A - 範例程序模組                                        |
//| 展示如何繼承 ProgramBase 並實現自定義邏輯                          |
//+------------------------------------------------------------------+
class CustomProgramA : public ProgramBase
{
private:
    int             m_executionCount;   // 執行次數計數器
    datetime        m_lastExecution;    // 上次執行時間
    bool           m_debugMode;         // 調試模式

public:
    //+------------------------------------------------------------------+
    //| 構造函數                                                          |
    //| 參數:                                                            |
    //|   type - 程序類型（默認為 TICK 階段）                            |
    //|   debugMode - 是否啟用調試模式                                   |
    //+------------------------------------------------------------------+
    CustomProgramA(ProgramType type = PROGRAM_TYPE_TICK, bool debugMode = false)
        : ProgramBase("CustomProgramA", type, ProgramManager::GetInstance()),
          m_executionCount(0),
          m_lastExecution(0),
          m_debugMode(debugMode)
    {
        LogInfo("CustomProgramA 已創建，類型: " + ProgramTypeToString(type));
    }
    
    //+------------------------------------------------------------------+
    //| 析構函數                                                          |
    //+------------------------------------------------------------------+
    ~CustomProgramA()
    {
        LogInfo("CustomProgramA 已銷毀，總執行次數: " + IntegerToString(m_executionCount));
    }
    
    //+------------------------------------------------------------------+
    //| 初始化程序                                                        |
    //| 重寫基類的初始化方法                                              |
    //| 返回值: 初始化是否成功                                           |
    //+------------------------------------------------------------------+
    virtual bool Initialize() override
    {
        if(!ProgramBase::Initialize())
            return false;
            
        m_executionCount = 0;
        m_lastExecution = 0;
        
        LogInfo("CustomProgramA 初始化完成");
        return true;
    }
    
    //+------------------------------------------------------------------+
    //| 執行程序邏輯                                                      |
    //| 實現基類的純虛函數                                               |
    //| 返回值: 執行是否成功                                             |
    //+------------------------------------------------------------------+
    virtual bool Execute() override
    {
        // 檢查程序是否已初始化和啟用
        if(!IsInitialized() || !IsEnabled())
        {
            if(m_debugMode)
                LogWarning("CustomProgramA 未初始化或未啟用，跳過執行");
            return false;
        }
        
        // 更新執行統計
        m_executionCount++;
        m_lastExecution = TimeCurrent();
        
        // 根據程序類型執行不同的邏輯
        switch(GetType())
        {
            case PROGRAM_TYPE_INIT:
                return ExecuteInitLogic();
                
            case PROGRAM_TYPE_TICK:
                return ExecuteTickLogic();
                
            case PROGRAM_TYPE_DEINIT:
                return ExecuteDeinitLogic();
                
            default:
                LogError("CustomProgramA 未知的程序類型: " + ProgramTypeToString(GetType()));
                return false;
        }
    }
    
    //+------------------------------------------------------------------+
    //| 終止程序                                                          |
    //| 重寫基類的終止方法                                               |
    //+------------------------------------------------------------------+
    virtual void Deinitialize() override
    {
        LogInfo("CustomProgramA 開始終止，執行次數: " + IntegerToString(m_executionCount));
        
        // 執行清理工作
        m_executionCount = 0;
        m_lastExecution = 0;
        
        ProgramBase::Deinitialize();
        LogInfo("CustomProgramA 終止完成");
    }
    
    //+------------------------------------------------------------------+
    //| 獲取執行次數                                                      |
    //| 返回值: 執行次數                                                 |
    //+------------------------------------------------------------------+
    int GetExecutionCount() const { return m_executionCount; }
    
    //+------------------------------------------------------------------+
    //| 獲取上次執行時間                                                  |
    //| 返回值: 上次執行時間                                             |
    //+------------------------------------------------------------------+
    datetime GetLastExecution() const { return m_lastExecution; }
    
    //+------------------------------------------------------------------+
    //| 設置調試模式                                                      |
    //| 參數:                                                            |
    //|   debugMode - 是否啟用調試模式                                   |
    //+------------------------------------------------------------------+
    void SetDebugMode(bool debugMode) { m_debugMode = debugMode; }
    
    //+------------------------------------------------------------------+
    //| 獲取程序詳細信息                                                  |
    //| 重寫基類方法                                                      |
    //| 返回值: 程序詳細信息字符串                                       |
    //+------------------------------------------------------------------+
    virtual string ToString() const override
    {
        return StringFormat("CustomProgramA [%s] - 執行次數: %d, 上次執行: %s, 調試模式: %s",
                          ProgramTypeToString(GetType()),
                          m_executionCount,
                          TimeToString(m_lastExecution),
                          m_debugMode ? "開啟" : "關閉");
    }

private:
    //+------------------------------------------------------------------+
    //| 執行初始化階段邏輯                                                |
    //| 返回值: 執行是否成功                                             |
    //+------------------------------------------------------------------+
    bool ExecuteInitLogic()
    {
        if(m_debugMode)
            LogInfo("CustomProgramA 執行初始化邏輯");
            
        // 在這裡添加初始化階段的具體邏輯
        // 例如：讀取配置、初始化變量等
        
        return true;
    }
    
    //+------------------------------------------------------------------+
    //| 執行週期階段邏輯                                                  |
    //| 返回值: 執行是否成功                                             |
    //+------------------------------------------------------------------+
    bool ExecuteTickLogic()
    {
        if(m_debugMode)
            LogInfo("CustomProgramA 執行週期邏輯，第 " + IntegerToString(m_executionCount) + " 次");
            
        // 在這裡添加週期執行的具體邏輯
        // 例如：檢查交易信號、執行交易等
        
        return true;
    }
    
    //+------------------------------------------------------------------+
    //| 執行終止階段邏輯                                                  |
    //| 返回值: 執行是否成功                                             |
    //+------------------------------------------------------------------+
    bool ExecuteDeinitLogic()
    {
        if(m_debugMode)
            LogInfo("CustomProgramA 執行終止邏輯");
            
        // 在這裡添加終止階段的具體邏輯
        // 例如：保存數據、關閉資源等
        
        return true;
    }
    
    //+------------------------------------------------------------------+
    //| 記錄警告信息                                                      |
    //| 參數:                                                            |
    //|   message - 警告消息                                            |
    //+------------------------------------------------------------------+
    void LogWarning(string message)
    {
        Print("WARNING [", GetName(), "]: ", message);
    }
};
