//+------------------------------------------------------------------+
//|                                               MainController.mqh |
//|                                    EA_Wizard 模組化程序系統      |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "ProgramManager.mqh"

//+------------------------------------------------------------------+
//| 主控制器類                                                        |
//| 對接 EA 核心函數，管理整個 EA 的生命週期                           |
//+------------------------------------------------------------------+
class MainController
{
private:
    ProgramManager*  m_manager;         // 程序管理器實例
    bool            m_initialized;      // 控制器是否已初始化
    string          m_eaName;          // EA 名稱
    string          m_version;         // EA 版本
    datetime        m_startTime;       // EA 啟動時間

public:
    //+------------------------------------------------------------------+
    //| 構造函數                                                          |
    //| 參數:                                                            |
    //|   eaName - EA 名稱                                              |
    //|   version - EA 版本                                             |
    //+------------------------------------------------------------------+
    MainController(string eaName = "EA_Wizard", string version = "1.0");

    //+------------------------------------------------------------------+
    //| 析構函數                                                          |
    //+------------------------------------------------------------------+
    ~MainController();

    //+------------------------------------------------------------------+
    //| EA 初始化函數                                                     |
    //| 對應 MQL4 的 OnInit() 函數                                        |
    //| 返回值: 初始化結果代碼                                           |
    //+------------------------------------------------------------------+
    int OnInit();

    //+------------------------------------------------------------------+
    //| EA 週期執行函數                                                   |
    //| 對應 MQL4 的 OnTick() 函數                                        |
    //+------------------------------------------------------------------+
    void OnTick();

    //+------------------------------------------------------------------+
    //| EA 終止函數                                                       |
    //| 對應 MQL4 的 OnDeinit() 函數                                      |
    //| 參數:                                                            |
    //|   reason - 終止原因                                             |
    //+------------------------------------------------------------------+
    void OnDeinit(const int reason);

    //+------------------------------------------------------------------+
    //| 獲取程序管理器                                                    |
    //| 返回值: 程序管理器指標                                           |
    //+------------------------------------------------------------------+
    ProgramManager* GetManager() const { return m_manager; }

    //+------------------------------------------------------------------+
    //| 檢查控制器是否已初始化                                            |
    //| 返回值: 如果已初始化返回 true                                     |
    //+------------------------------------------------------------------+
    bool IsInitialized() const { return m_initialized; }

    //+------------------------------------------------------------------+
    //| 獲取 EA 名稱                                                      |
    //| 返回值: EA 名稱                                                   |
    //+------------------------------------------------------------------+
    string GetEAName() const { return m_eaName; }

    //+------------------------------------------------------------------+
    //| 獲取 EA 版本                                                      |
    //| 返回值: EA 版本                                                   |
    //+------------------------------------------------------------------+
    string GetVersion() const { return m_version; }

    //+------------------------------------------------------------------+
    //| 獲取 EA 運行時間                                                  |
    //| 返回值: EA 運行時間（秒）                                         |
    //+------------------------------------------------------------------+
    int GetRunningTime() const;

    //+------------------------------------------------------------------+
    //| 打印 EA 狀態信息                                                  |
    //+------------------------------------------------------------------+
    void PrintStatus();

protected:
    //+------------------------------------------------------------------+
    //| 初始化程序管理器                                                  |
    //| 返回值: 初始化是否成功                                           |
    //+------------------------------------------------------------------+
    bool InitializeManager();

    //+------------------------------------------------------------------+
    //| 創建默認程序模組                                                  |
    //| 子類可以重寫此方法來創建自定義的程序模組                          |
    //+------------------------------------------------------------------+
    virtual void CreateDefaultPrograms();

    //+------------------------------------------------------------------+
    //| 記錄日誌信息                                                      |
    //| 參數:                                                            |
    //|   message - 日誌消息                                            |
    //+------------------------------------------------------------------+
    void LogInfo(string message);

    //+------------------------------------------------------------------+
    //| 記錄錯誤信息                                                      |
    //| 參數:                                                            |
    //|   message - 錯誤消息                                            |
    //+------------------------------------------------------------------+
    void LogError(string message);

    //+------------------------------------------------------------------+
    //| 記錄警告信息                                                      |
    //| 參數:                                                            |
    //|   message - 警告消息                                            |
    //+------------------------------------------------------------------+
    void LogWarning(string message);

private:
    //+------------------------------------------------------------------+
    //| 格式化時間為字符串                                                |
    //| 參數:                                                            |
    //|   time - 時間值                                                 |
    //| 返回值: 格式化的時間字符串                                       |
    //+------------------------------------------------------------------+
    string FormatTime(datetime time);

    //+------------------------------------------------------------------+
    //| 獲取終止原因字符串                                                |
    //| 參數:                                                            |
    //|   reason - 終止原因代碼                                         |
    //| 返回值: 終止原因的字符串描述                                     |
    //+------------------------------------------------------------------+
    string GetDeinitReasonString(int reason);
};

//+------------------------------------------------------------------+
//| MainController 類實現                                             |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 構造函數實現                                                      |
//+------------------------------------------------------------------+
MainController::MainController(string eaName = "EA_Wizard", string version = "1.0")
    : m_manager(NULL),
      m_initialized(false),
      m_eaName(eaName),
      m_version(version),
      m_startTime(0)
{
    LogInfo("主控制器已創建 - " + m_eaName + " v" + m_version);
}

//+------------------------------------------------------------------+
//| 析構函數實現                                                      |
//+------------------------------------------------------------------+
MainController::~MainController()
{
    LogInfo("主控制器正在銷毀");

    if(m_initialized)
    {
        OnDeinit(REASON_PROGRAM);
    }
}

//+------------------------------------------------------------------+
//| EA 初始化函數實現                                                 |
//+------------------------------------------------------------------+
int MainController::OnInit()
{
    LogInfo("=== EA 初始化開始 ===");
    LogInfo("EA 名稱: " + m_eaName);
    LogInfo("EA 版本: " + m_version);
    LogInfo("初始化時間: " + FormatTime(TimeCurrent()));

    m_startTime = TimeCurrent();

    // 初始化程序管理器
    if(!InitializeManager())
    {
        LogError("程序管理器初始化失敗");
        return INIT_FAILED;
    }

    // 創建默認程序模組
    CreateDefaultPrograms();

    // 執行初始化階段程序
    int result = m_manager.RunInit();
    if(result != INIT_SUCCEEDED)
    {
        LogError("初始化階段程序執行失敗，代碼: " + IntegerToString(result));
        return result;
    }

    m_initialized = true;
    LogInfo("=== EA 初始化完成 ===");
    PrintStatus();

    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| EA 週期執行函數實現                                               |
//+------------------------------------------------------------------+
void MainController::OnTick()
{
    if(!m_initialized)
    {
        LogError("EA 未初始化，無法執行週期函數");
        return;
    }

    if(m_manager == NULL)
    {
        LogError("程序管理器為空，無法執行週期函數");
        return;
    }

    // 執行週期階段程序
    if(!m_manager.RunTick())
    {
        LogWarning("週期階段程序執行失敗");
    }
}

//+------------------------------------------------------------------+
//| EA 終止函數實現                                                   |
//+------------------------------------------------------------------+
void MainController::OnDeinit(const int reason)
{
    LogInfo("=== EA 終止開始 ===");
    LogInfo("終止原因: " + GetDeinitReasonString(reason));
    LogInfo("運行時間: " + IntegerToString(GetRunningTime()) + " 秒");

    if(m_manager != NULL)
    {
        // 執行終止階段程序
        m_manager.RunDeinit(reason);

        // 打印最終狀態
        m_manager.PrintProgramInfo();

        // 釋放程序管理器
        ProgramManager::ReleaseInstance();
        m_manager = NULL;
    }

    m_initialized = false;
    LogInfo("=== EA 終止完成 ===");
}

//+------------------------------------------------------------------+
//| 獲取 EA 運行時間實現                                              |
//+------------------------------------------------------------------+
int MainController::GetRunningTime() const
{
    if(m_startTime == 0)
        return 0;
    return (int)(TimeCurrent() - m_startTime);
}

//+------------------------------------------------------------------+
//| 打印 EA 狀態信息實現                                              |
//+------------------------------------------------------------------+
void MainController::PrintStatus()
{
    LogInfo("=== EA 狀態信息 ===");
    LogInfo("EA 名稱: " + m_eaName);
    LogInfo("EA 版本: " + m_version);
    LogInfo("初始化狀態: " + (m_initialized ? "已初始化" : "未初始化"));
    LogInfo("啟動時間: " + FormatTime(m_startTime));
    LogInfo("運行時間: " + IntegerToString(GetRunningTime()) + " 秒");

    if(m_manager != NULL)
    {
        m_manager.PrintProgramInfo();
    }
}

//+------------------------------------------------------------------+
//| 初始化程序管理器實現                                              |
//+------------------------------------------------------------------+
bool MainController::InitializeManager()
{
    if(m_manager != NULL)
    {
        LogWarning("程序管理器已經初始化");
        return true;
    }

    m_manager = ProgramManager::GetInstance();
    if(m_manager == NULL)
    {
        LogError("無法獲取程序管理器實例");
        return false;
    }

    if(!m_manager.IsInitialized())
    {
        LogError("程序管理器初始化失敗");
        return false;
    }

    LogInfo("程序管理器初始化成功");
    return true;
}

//+------------------------------------------------------------------+
//| 創建默認程序模組實現                                              |
//+------------------------------------------------------------------+
void MainController::CreateDefaultPrograms()
{
    LogInfo("創建默認程序模組");

    // 子類可以重寫此方法來創建自定義的程序模組
    // 這裡可以創建一些基本的程序模組，例如：
    // - 日誌記錄程序
    // - 狀態監控程序
    // - 錯誤處理程序等

    LogInfo("默認程序模組創建完成");
}

//+------------------------------------------------------------------+
//| 記錄日誌信息實現                                                  |
//+------------------------------------------------------------------+
void MainController::LogInfo(string message)
{
    Print("INFO [MainController]: ", message);
}

//+------------------------------------------------------------------+
//| 記錄錯誤信息實現                                                  |
//+------------------------------------------------------------------+
void MainController::LogError(string message)
{
    Print("ERROR [MainController]: ", message);
}

//+------------------------------------------------------------------+
//| 記錄警告信息實現                                                  |
//+------------------------------------------------------------------+
void MainController::LogWarning(string message)
{
    Print("WARNING [MainController]: ", message);
}

//+------------------------------------------------------------------+
//| 格式化時間為字符串實現                                            |
//+------------------------------------------------------------------+
string MainController::FormatTime(datetime time)
{
    return TimeToString(time, TIME_DATE | TIME_MINUTES | TIME_SECONDS);
}

//+------------------------------------------------------------------+
//| 獲取終止原因字符串實現                                            |
//+------------------------------------------------------------------+
string MainController::GetDeinitReasonString(int reason)
{
    switch(reason)
    {
        case REASON_PROGRAM:     return "程序終止";
        case REASON_REMOVE:      return "EA 被移除";
        case REASON_RECOMPILE:   return "EA 重新編譯";
        case REASON_CHARTCHANGE: return "圖表變更";
        case REASON_CHARTCLOSE:  return "圖表關閉";
        case REASON_PARAMETERS:  return "參數變更";
        case REASON_ACCOUNT:     return "帳戶變更";
        case REASON_TEMPLATE:    return "模板變更";
        case REASON_INITFAILED:  return "初始化失敗";
        case REASON_CLOSE:       return "終端關閉";
        default:                 return "未知原因 (" + IntegerToString(reason) + ")";
    }
}
