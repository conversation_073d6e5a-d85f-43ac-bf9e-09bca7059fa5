//+------------------------------------------------------------------+
//|                                                       TestEA.mq4 |
//|                                    EA_Wizard 模組化程序系統測試   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

// 包含程序範例
#include "ProgramExample.mqh"

//+------------------------------------------------------------------+
//| EA 輸入參數                                                       |
//+------------------------------------------------------------------+
input bool    EnableDebugMode = true;      // 啟用調試模式
input int     StatusCheckInterval = 30;    // 狀態檢查間隔（秒）

//+------------------------------------------------------------------+
//| EA 初始化函數                                                     |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== EA_Wizard 模組化程序系統測試開始 ===");
    Print("調試模式: ", EnableDebugMode ? "開啟" : "關閉");
    Print("狀態檢查間隔: ", StatusCheckInterval, " 秒");
    
    // 調用範例初始化函數
    int result = ExampleOnInit();
    
    if(result == INIT_SUCCEEDED)
    {
        Print("EA 初始化成功");
        
        // 創建額外的程序模組
        CreateAdditionalPrograms();
        
        // 打印系統狀態
        PrintSystemStatus();
    }
    else
    {
        Print("EA 初始化失敗，代碼: ", result);
    }
    
    return result;
}

//+------------------------------------------------------------------+
//| EA 週期執行函數                                                   |
//+------------------------------------------------------------------+
void OnTick()
{
    // 調用範例週期函數
    ExampleOnTick();
}

//+------------------------------------------------------------------+
//| EA 終止函數                                                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("=== EA_Wizard 模組化程序系統測試結束 ===");
    Print("終止原因: ", reason);
    
    // 調用範例終止函數
    ExampleOnDeinit(reason);
}

//+------------------------------------------------------------------+
//| 創建額外的程序模組                                                |
//+------------------------------------------------------------------+
void CreateAdditionalPrograms()
{
    Print("創建額外的程序模組...");
    
    // 獲取程序管理器
    ProgramManager* manager = g_controller.GetManager();
    if(manager == NULL)
    {
        Print("錯誤: 無法獲取程序管理器");
        return;
    }
    
    // 創建日誌記錄程序
    LoggerProgram* logger = new LoggerProgram(PROGRAM_TYPE_TICK);
    if(logger != NULL)
    {
        manager.Register(logger);
        Print("日誌記錄程序已創建並註冊");
    }
    
    // 創建狀態監控程序
    StatusMonitorProgram* monitor = new StatusMonitorProgram(StatusCheckInterval);
    if(monitor != NULL)
    {
        manager.Register(monitor);
        Print("狀態監控程序已創建並註冊");
    }
    
    Print("額外程序模組創建完成");
}

//+------------------------------------------------------------------+
//| 專家顧問測試函數                                                  |
//+------------------------------------------------------------------+
void OnTester()
{
    Print("=== EA 測試完成 ===");
    PrintSystemStatus();
}

//+------------------------------------------------------------------+
//| 圖表事件處理函數                                                  |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
{
    // 處理圖表事件
    if(id == CHARTEVENT_KEYDOWN)
    {
        if(lparam == 'S' || lparam == 's') // 按 S 鍵顯示狀態
        {
            Print("=== 手動狀態檢查 ===");
            PrintSystemStatus();
        }
    }
}

//+------------------------------------------------------------------+
//| 定時器事件處理函數                                                |
//+------------------------------------------------------------------+
void OnTimer()
{
    // 定期打印狀態信息
    static int timerCount = 0;
    timerCount++;
    
    if(timerCount % 10 == 0) // 每10次定時器事件打印一次狀態
    {
        Print("定時器狀態檢查 #", timerCount);
        PrintSystemStatus();
    }
}

//+------------------------------------------------------------------+
//| 交易事件處理函數                                                  |
//+------------------------------------------------------------------+
void OnTrade()
{
    Print("交易事件觸發");
    // 在這裡可以添加交易相關的程序模組處理邏輯
}

//+------------------------------------------------------------------+
//| 書籤事件處理函數                                                  |
//+------------------------------------------------------------------+
void OnBookEvent(const string &symbol)
{
    Print("市場深度變化: ", symbol);
    // 在這裡可以添加市場深度相關的程序模組處理邏輯
}
