//+------------------------------------------------------------------+
//|                                                  ProgramType.mqh |
//|                                    EA_Wizard 模組化程序系統      |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//| 程序類型枚舉                                                      |
//| 定義程序模組在 EA 生命週期中的執行階段                            |
//+------------------------------------------------------------------+
enum ProgramType
{
    PROGRAM_TYPE_INIT,      // 初始化階段 - 在 OnInit() 中執行
    PROGRAM_TYPE_TICK,      // 週期執行階段 - 在 OnTick() 中執行  
    PROGRAM_TYPE_DEINIT     // 終止階段 - 在 OnDeinit() 中執行
};

//+------------------------------------------------------------------+
//| 程序類型工具函數                                                  |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 將程序類型轉換為字符串                                            |
//| 參數:                                                            |
//|   type - 程序類型                                               |
//| 返回值: 程序類型的字符串表示                                      |
//+------------------------------------------------------------------+
string ProgramTypeToString(ProgramType type)
{
    switch(type)
    {
        case PROGRAM_TYPE_INIT:   return "INIT";
        case PROGRAM_TYPE_TICK:   return "TICK";
        case PROGRAM_TYPE_DEINIT: return "DEINIT";
        default:                  return "UNKNOWN";
    }
}

//+------------------------------------------------------------------+
//| 從字符串解析程序類型                                              |
//| 參數:                                                            |
//|   typeStr - 程序類型字符串                                       |
//| 返回值: 對應的程序類型，如果無法解析則返回 PROGRAM_TYPE_TICK      |
//+------------------------------------------------------------------+
ProgramType StringToProgramType(string typeStr)
{
    if(typeStr == "INIT")        return PROGRAM_TYPE_INIT;
    if(typeStr == "TICK")        return PROGRAM_TYPE_TICK;
    if(typeStr == "DEINIT")      return PROGRAM_TYPE_DEINIT;
    
    // 默認返回 TICK 類型
    return PROGRAM_TYPE_TICK;
}

//+------------------------------------------------------------------+
//| 檢查程序類型是否有效                                              |
//| 參數:                                                            |
//|   type - 程序類型                                               |
//| 返回值: 如果類型有效返回 true，否則返回 false                     |
//+------------------------------------------------------------------+
bool IsValidProgramType(ProgramType type)
{
    return (type >= PROGRAM_TYPE_INIT && type <= PROGRAM_TYPE_DEINIT);
}
