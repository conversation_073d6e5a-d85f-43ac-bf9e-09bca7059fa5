//+------------------------------------------------------------------+
//|                                             ProgramExample.mqh |
//|                                    EA_Wizard 模組化程序系統      |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "MainController.mqh"
#include "CustomProgramA.mqh"

//+------------------------------------------------------------------+
//| 範例主控制器類                                                    |
//| 展示如何擴展 MainController 並創建自定義程序模組                   |
//+------------------------------------------------------------------+
class ExampleController : public MainController
{
private:
    CustomProgramA*  m_initProgram;     // 初始化階段程序
    CustomProgramA*  m_tickProgram;     // 週期階段程序
    CustomProgramA*  m_deinitProgram;   // 終止階段程序

public:
    //+------------------------------------------------------------------+
    //| 構造函數                                                          |
    //+------------------------------------------------------------------+
    ExampleController() : MainController("EA_Wizard_Example", "1.0")
    {
        m_initProgram = NULL;
        m_tickProgram = NULL;
        m_deinitProgram = NULL;
    }
    
    //+------------------------------------------------------------------+
    //| 析構函數                                                          |
    //+------------------------------------------------------------------+
    ~ExampleController()
    {
        // 清理程序實例
        if(m_initProgram != NULL)
        {
            delete m_initProgram;
            m_initProgram = NULL;
        }
        
        if(m_tickProgram != NULL)
        {
            delete m_tickProgram;
            m_tickProgram = NULL;
        }
        
        if(m_deinitProgram != NULL)
        {
            delete m_deinitProgram;
            m_deinitProgram = NULL;
        }
    }

protected:
    //+------------------------------------------------------------------+
    //| 創建默認程序模組實現                                              |
    //| 重寫基類方法，創建自定義程序模組                                   |
    //+------------------------------------------------------------------+
    virtual void CreateDefaultPrograms() override
    {
        LogInfo("創建範例程序模組");
        
        // 創建初始化階段程序
        m_initProgram = new CustomProgramA(PROGRAM_TYPE_INIT, true);
        if(m_initProgram != NULL)
        {
            // 手動註冊程序（因為自動註冊被簡化了）
            if(GetManager() != NULL)
            {
                GetManager().Register(m_initProgram);
            }
            LogInfo("初始化程序已創建並註冊");
        }
        
        // 創建週期階段程序
        m_tickProgram = new CustomProgramA(PROGRAM_TYPE_TICK, false);
        if(m_tickProgram != NULL)
        {
            if(GetManager() != NULL)
            {
                GetManager().Register(m_tickProgram);
            }
            LogInfo("週期程序已創建並註冊");
        }
        
        // 創建終止階段程序
        m_deinitProgram = new CustomProgramA(PROGRAM_TYPE_DEINIT, true);
        if(m_deinitProgram != NULL)
        {
            if(GetManager() != NULL)
            {
                GetManager().Register(m_deinitProgram);
            }
            LogInfo("終止程序已創建並註冊");
        }
        
        LogInfo("範例程序模組創建完成");
    }
};

//+------------------------------------------------------------------+
//| 全局控制器實例                                                    |
//+------------------------------------------------------------------+
ExampleController g_controller;

//+------------------------------------------------------------------+
//| 範例使用函數                                                      |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| EA 初始化函數                                                     |
//| 在 EA 的 OnInit() 中調用此函數                                    |
//+------------------------------------------------------------------+
int ExampleOnInit()
{
    Print("=== EA_Wizard 模組化程序系統範例 ===");
    return g_controller.OnInit();
}

//+------------------------------------------------------------------+
//| EA 週期執行函數                                                   |
//| 在 EA 的 OnTick() 中調用此函數                                    |
//+------------------------------------------------------------------+
void ExampleOnTick()
{
    g_controller.OnTick();
}

//+------------------------------------------------------------------+
//| EA 終止函數                                                       |
//| 在 EA 的 OnDeinit() 中調用此函數                                  |
//+------------------------------------------------------------------+
void ExampleOnDeinit(const int reason)
{
    g_controller.OnDeinit(reason);
}

//+------------------------------------------------------------------+
//| 打印系統狀態                                                      |
//| 可以在 EA 中調用此函數來查看系統狀態                               |
//+------------------------------------------------------------------+
void PrintSystemStatus()
{
    g_controller.PrintStatus();
}

//+------------------------------------------------------------------+
//| 範例程序模組類 - 日誌記錄程序                                      |
//+------------------------------------------------------------------+
class LoggerProgram : public ProgramBase
{
private:
    string m_logFile;
    
public:
    LoggerProgram(ProgramType type = PROGRAM_TYPE_TICK) 
        : ProgramBase("LoggerProgram", type, ProgramManager::GetInstance())
    {
        m_logFile = "EA_Wizard_Log.txt";
    }
    
    virtual bool Execute() override
    {
        if(!IsInitialized() || !IsEnabled())
            return false;
            
        switch(GetType())
        {
            case PROGRAM_TYPE_INIT:
                LogInfo("日誌系統初始化");
                return true;
                
            case PROGRAM_TYPE_TICK:
                // 每隔一段時間記錄狀態
                static datetime lastLog = 0;
                if(TimeCurrent() - lastLog > 60) // 每分鐘記錄一次
                {
                    LogInfo("系統運行正常 - " + TimeToString(TimeCurrent()));
                    lastLog = TimeCurrent();
                }
                return true;
                
            case PROGRAM_TYPE_DEINIT:
                LogInfo("日誌系統終止");
                return true;
        }
        
        return false;
    }
};

//+------------------------------------------------------------------+
//| 範例程序模組類 - 狀態監控程序                                      |
//+------------------------------------------------------------------+
class StatusMonitorProgram : public ProgramBase
{
private:
    int m_checkInterval;
    datetime m_lastCheck;
    
public:
    StatusMonitorProgram(int checkInterval = 30) 
        : ProgramBase("StatusMonitor", PROGRAM_TYPE_TICK, ProgramManager::GetInstance()),
          m_checkInterval(checkInterval),
          m_lastCheck(0)
    {
    }
    
    virtual bool Execute() override
    {
        if(!IsInitialized() || !IsEnabled())
            return false;
            
        datetime currentTime = TimeCurrent();
        if(currentTime - m_lastCheck < m_checkInterval)
            return true;
            
        m_lastCheck = currentTime;
        
        // 檢查系統狀態
        LogInfo("狀態檢查 - 帳戶餘額: " + DoubleToString(AccountBalance(), 2));
        LogInfo("狀態檢查 - 當前時間: " + TimeToString(currentTime));
        
        return true;
    }
};
