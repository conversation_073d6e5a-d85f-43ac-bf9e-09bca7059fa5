//+------------------------------------------------------------------+
//|                                              TradingPipeline.mqh |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "TradingEvent.mqh"

//+------------------------------------------------------------------+
//| 交易流水線接口                                                   |
//+------------------------------------------------------------------+
interface ITradingPipeline
{
    // 執行流水線
    bool Execute();
    
    // 獲取流水線名稱
    string GetName();
    
    // 獲取流水線類型
    string GetType();
    
    // 檢查是否已完成
    bool IsCompleted();
    
    // 檢查是否有錯誤
    bool HasError();
    
    // 獲取最後錯誤信息
    string GetLastError();
    
    // 獲取執行狀態
    ENUM_PIPELINE_STATUS GetStatus();
    
    // 重置流水線狀態
    void Reset();
};

//+------------------------------------------------------------------+
//| 流水線執行結果                                                   |
//+------------------------------------------------------------------+
class PipelineResult
{
private:
    bool m_success;                 // 執行是否成功
    string m_message;               // 結果消息
    string m_source;                // 來源流水線
    datetime m_timestamp;           // 執行時間戳
    ENUM_ERROR_LEVEL m_errorLevel;  // 錯誤級別

public:
    // 構造函數
    PipelineResult(bool success, string message, string source, ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_INFO)
        : m_success(success), 
          m_message(message), 
          m_source(source),
          m_timestamp(TimeCurrent()),
          m_errorLevel(errorLevel)
    {
    }
    
    // 析構函數
    ~PipelineResult() {}
    
    // 獲取執行結果
    bool IsSuccess() const { return m_success; }
    
    // 獲取消息
    string GetMessage() const { return m_message; }
    
    // 獲取來源
    string GetSource() const { return m_source; }
    
    // 獲取時間戳
    datetime GetTimestamp() const { return m_timestamp; }
    
    // 獲取錯誤級別
    ENUM_ERROR_LEVEL GetErrorLevel() const { return m_errorLevel; }
    
    // 轉換為字符串
    string ToString() const
    {
        return StringFormat("[%s] %s: %s (Level: %d)", 
                          TimeToString(m_timestamp), 
                          m_source, 
                          m_message, 
                          m_errorLevel);
    }
};

//+------------------------------------------------------------------+
//| 交易流水線基類                                                   |
//+------------------------------------------------------------------+
class TradingPipeline : public ITradingPipeline
{
protected:
    string m_name;                      // 流水線名稱
    string m_type;                      // 流水線類型
    ENUM_PIPELINE_STATUS m_status;      // 執行狀態
    PipelineResult* m_lastResult;       // 最後執行結果
    datetime m_lastExecutionTime;       // 最後執行時間
    int m_executionCount;               // 執行次數
    bool m_isEnabled;                   // 是否啟用

public:
    // 構造函數
    TradingPipeline(string name, string type = TRADING_PIPELINE_TYPE)
        : m_name(name),
          m_type(type),
          m_status(STATUS_PENDING),
          m_lastResult(NULL),
          m_lastExecutionTime(0),
          m_executionCount(0),
          m_isEnabled(true)
    {
    }
    
    // 析構函數
    virtual ~TradingPipeline()
    {
        if(m_lastResult != NULL)
        {
            delete m_lastResult;
            m_lastResult = NULL;
        }
    }
    
    // 執行流水線（模板方法）
    virtual bool Execute() override
    {
        if(!m_isEnabled)
        {
            SetResult(false, "流水線已禁用", ERROR_LEVEL_WARNING);
            return false;
        }
        
        if(m_status == STATUS_RUNNING)
        {
            SetResult(false, "流水線正在執行中", ERROR_LEVEL_WARNING);
            return false;
        }
        
        m_status = STATUS_RUNNING;
        m_lastExecutionTime = TimeCurrent();
        m_executionCount++;
        
        bool success = DoExecute();
        
        m_status = success ? STATUS_COMPLETED : STATUS_FAILED;
        
        return success;
    }
    
    // 獲取流水線名稱
    virtual string GetName() override { return m_name; }
    
    // 獲取流水線類型
    virtual string GetType() override { return m_type; }
    
    // 檢查是否已完成
    virtual bool IsCompleted() override 
    { 
        return m_status == STATUS_COMPLETED; 
    }
    
    // 檢查是否有錯誤
    virtual bool HasError() override 
    { 
        return m_status == STATUS_FAILED || 
               (m_lastResult != NULL && !m_lastResult.IsSuccess()); 
    }
    
    // 獲取最後錯誤信息
    virtual string GetLastError() override
    {
        if(m_lastResult != NULL)
        {
            return m_lastResult.GetMessage();
        }
        return "";
    }
    
    // 重置流水線狀態
    virtual void Reset() override
    {
        m_status = STATUS_PENDING;
        if(m_lastResult != NULL)
        {
            delete m_lastResult;
            m_lastResult = NULL;
        }
        m_lastExecutionTime = 0;
        m_executionCount = 0;
    }
    
    // 獲取執行狀態
    virtual ENUM_PIPELINE_STATUS GetStatus() override { return m_status; }
    
    // 啟用/禁用流水線
    void SetEnabled(bool enabled) { m_isEnabled = enabled; }
    bool IsEnabled() const { return m_isEnabled; }
    
    // 獲取執行統計
    int GetExecutionCount() const { return m_executionCount; }
    datetime GetLastExecutionTime() const { return m_lastExecutionTime; }
    
    // 獲取最後結果
    PipelineResult* GetLastResult() const { return m_lastResult; }

protected:
    // 實際執行邏輯（由子類實現）
    virtual bool DoExecute() = 0;
    
    // 設置執行結果
    void SetResult(bool success, string message, ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_INFO)
    {
        if(m_lastResult != NULL)
        {
            delete m_lastResult;
        }
        m_lastResult = new PipelineResult(success, message, m_name, errorLevel);
    }
    
    // 記錄日誌（可被子類覆蓋）
    virtual void LogMessage(string message, ENUM_ERROR_LEVEL level = ERROR_LEVEL_INFO)
    {
        string prefix = "";
        switch(level)
        {
            case ERROR_LEVEL_INFO:     prefix = "[INFO]"; break;
            case ERROR_LEVEL_WARNING:  prefix = "[WARN]"; break;
            case ERROR_LEVEL_ERROR:    prefix = "[ERROR]"; break;
            case ERROR_LEVEL_CRITICAL: prefix = "[CRITICAL]"; break;
        }
        
        Print(prefix, " [", m_name, "] ", message);
    }
};

//+------------------------------------------------------------------+
//| 簡單交易流水線實現示例                                           |
//+------------------------------------------------------------------+
class SimpleTradingPipeline : public TradingPipeline
{
private:
    string m_task;  // 任務描述

public:
    // 構造函數
    SimpleTradingPipeline(string name, string task)
        : TradingPipeline(name, "SimpleTradingPipeline"),
          m_task(task)
    {
    }
    
    // 析構函數
    ~SimpleTradingPipeline() {}

protected:
    // 實現具體的執行邏輯
    virtual bool DoExecute() override
    {
        LogMessage("開始執行任務: " + m_task);
        
        // 模擬任務執行
        Sleep(10); // 簡單的延遲模擬
        
        // 這裡可以添加實際的業務邏輯
        bool success = true; // 假設總是成功
        
        if(success)
        {
            SetResult(true, "任務執行成功: " + m_task);
            LogMessage("任務執行完成: " + m_task);
        }
        else
        {
            SetResult(false, "任務執行失敗: " + m_task, ERROR_LEVEL_ERROR);
            LogMessage("任務執行失敗: " + m_task, ERROR_LEVEL_ERROR);
        }
        
        return success;
    }
};
