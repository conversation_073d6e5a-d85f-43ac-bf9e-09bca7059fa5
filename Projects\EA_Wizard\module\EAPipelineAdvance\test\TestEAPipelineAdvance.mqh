//+------------------------------------------------------------------+
//|                                       TestEAPipelineAdvance.mqh |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../EAPipelineAdvance.mqh"

//+------------------------------------------------------------------+
//| 測試用的具體流水線實現                                           |
//+------------------------------------------------------------------+
class TestDataFeedPipeline : public TradingPipeline
{
public:
    TestDataFeedPipeline() : TradingPipeline("DataFeed", "TestDataFeedPipeline") {}

protected:
    virtual bool DoExecute() override
    {
        LogMessage("執行數據饋送流水線");
        Sleep(100); // 模擬處理時間
        SetResult(true, "數據饋送完成");
        return true;
    }
};

class TestSignalPipeline : public TradingPipeline
{
public:
    TestSignalPipeline() : TradingPipeline("Signal", "TestSignalPipeline") {}

protected:
    virtual bool DoExecute() override
    {
        LogMessage("執行信號分析流水線");
        Sleep(150); // 模擬處理時間
        SetResult(true, "信號分析完成");
        return true;
    }
};

class TestOrderPipeline : public TradingPipeline
{
public:
    TestOrderPipeline() : TradingPipeline("Order", "TestOrderPipeline") {}

protected:
    virtual bool DoExecute() override
    {
        LogMessage("執行訂單處理流水線");
        Sleep(200); // 模擬處理時間
        SetResult(true, "訂單處理完成");
        return true;
    }
};

class TestFailingPipeline : public TradingPipeline
{
public:
    TestFailingPipeline() : TradingPipeline("FailingTest", "TestFailingPipeline") {}

protected:
    virtual bool DoExecute() override
    {
        LogMessage("執行失敗測試流水線", ERROR_LEVEL_WARNING);
        SetResult(false, "模擬執行失敗", ERROR_LEVEL_ERROR);
        return false;
    }
};

//+------------------------------------------------------------------+
//| 測試框架                                                         |
//+------------------------------------------------------------------+
class TestFramework
{
private:
    int m_totalTests;
    int m_passedTests;
    int m_failedTests;

public:
    TestFramework() : m_totalTests(0), m_passedTests(0), m_failedTests(0) {}
    
    void RunTest(string testName, bool result)
    {
        m_totalTests++;
        if(result)
        {
            m_passedTests++;
            Print("✅ ", testName, " - PASSED");
        }
        else
        {
            m_failedTests++;
            Print("❌ ", testName, " - FAILED");
        }
    }
    
    void PrintSummary()
    {
        Print("=== 測試摘要 ===");
        Print("總測試數: ", m_totalTests);
        Print("通過: ", m_passedTests);
        Print("失敗: ", m_failedTests);
        Print("成功率: ", m_totalTests > 0 ? (double)m_passedTests / m_totalTests * 100.0 : 0.0, "%");
        Print("===============");
    }
};

//+------------------------------------------------------------------+
//| 測試類                                                           |
//+------------------------------------------------------------------+
class EAPipelineAdvanceTest
{
private:
    TestFramework m_framework;

public:
    // 運行所有測試
    void RunAllTests()
    {
        Print("開始 EAPipelineAdvance 模組測試...");
        
        TestBasicPipeline();
        TestCompositePipeline();
        TestPipelineExecutor();
        TestLoggingPipeline();
        TestPipelineRegistry();
        TestPipelineGroup();
        TestModuleManager();
        TestErrorHandling();
        
        m_framework.PrintSummary();
    }

private:
    // 測試基本流水線
    void TestBasicPipeline()
    {
        Print("\n--- 測試基本流水線 ---");
        
        TestDataFeedPipeline* pipeline = new TestDataFeedPipeline();
        
        // 測試初始狀態
        m_framework.RunTest("初始狀態檢查", 
                           pipeline.GetStatus() == STATUS_PENDING && 
                           !pipeline.IsCompleted() && 
                           !pipeline.HasError());
        
        // 測試執行
        bool success = pipeline.Execute();
        m_framework.RunTest("流水線執行", success);
        m_framework.RunTest("執行後狀態檢查", 
                           pipeline.GetStatus() == STATUS_COMPLETED && 
                           pipeline.IsCompleted());
        
        // 測試重置
        pipeline.Reset();
        m_framework.RunTest("重置後狀態檢查", 
                           pipeline.GetStatus() == STATUS_PENDING && 
                           !pipeline.IsCompleted());
        
        delete pipeline;
    }
    
    // 測試複合流水線
    void TestCompositePipeline()
    {
        Print("\n--- 測試複合流水線 ---");
        
        CompositePipeline* composite = new CompositePipeline("TestComposite");
        TestDataFeedPipeline* dataFeed = new TestDataFeedPipeline();
        TestSignalPipeline* signal = new TestSignalPipeline();
        TestOrderPipeline* order = new TestOrderPipeline();
        
        // 測試添加子流水線
        bool added1 = composite.Add(dataFeed);
        bool added2 = composite.Add(signal);
        bool added3 = composite.Add(order);
        
        m_framework.RunTest("添加子流水線", added1 && added2 && added3);
        m_framework.RunTest("子流水線數量檢查", composite.GetChildCount() == 3);
        
        // 測試執行
        bool success = composite.Execute();
        m_framework.RunTest("複合流水線執行", success);
        
        // 測試查找
        ITradingPipeline* found = composite.FindByName("DataFeed");
        m_framework.RunTest("按名稱查找子流水線", found != NULL && found == dataFeed);
        
        delete composite;
        delete dataFeed;
        delete signal;
        delete order;
    }
    
    // 測試流水線執行器
    void TestPipelineExecutor()
    {
        Print("\n--- 測試流水線執行器 ---");
        
        PipelineExecutor* executor = new PipelineExecutor("TestExecutor");
        TestDataFeedPipeline* pipeline1 = new TestDataFeedPipeline();
        TestSignalPipeline* pipeline2 = new TestSignalPipeline();
        
        // 測試添加流水線
        bool added1 = executor.AddPipeline(pipeline1);
        bool added2 = executor.AddPipeline(pipeline2);
        
        m_framework.RunTest("添加流水線到執行器", added1 && added2);
        m_framework.RunTest("執行器流水線數量檢查", executor.GetPipelineCount() == 2);
        
        // 測試執行所有流水線
        bool success = executor.ExecuteAll();
        m_framework.RunTest("執行器執行所有流水線", success);
        
        // 測試統計信息
        ExecutionStats* stats = executor.GetStats();
        m_framework.RunTest("執行統計檢查", 
                           stats.GetTotalExecuted() == 2 && 
                           stats.GetSuccessCount() == 2);
        
        delete executor;
        delete pipeline1;
        delete pipeline2;
    }
    
    // 測試日誌流水線
    void TestLoggingPipeline()
    {
        Print("\n--- 測試日誌流水線 ---");
        
        TestDataFeedPipeline* basePipeline = new TestDataFeedPipeline();
        LoggingPipeline* loggingPipeline = new LoggingPipeline(basePipeline);
        
        // 測試執行
        bool success = loggingPipeline.Execute();
        m_framework.RunTest("日誌流水線執行", success);
        
        // 測試包裝的流水線
        ITradingPipeline* wrapped = loggingPipeline.GetWrappedPipeline();
        m_framework.RunTest("獲取包裝的流水線", wrapped == basePipeline);
        
        delete loggingPipeline;
        delete basePipeline;
    }
    
    // 測試流水線註冊器
    void TestPipelineRegistry()
    {
        Print("\n--- 測試流水線註冊器 ---");
        
        TradingPipelineRegistry* registry = new TradingPipelineRegistry("TestRegistry");
        TestDataFeedPipeline* pipeline = new TestDataFeedPipeline();
        
        // 測試註冊
        RegistryResult* result = registry.RegisterPipeline(pipeline);
        m_framework.RunTest("註冊流水線", result.IsSuccess());
        delete result;
        
        // 測試獲取
        ITradingPipeline* retrieved = registry.GetPipeline("DataFeed");
        m_framework.RunTest("獲取註冊的流水線", retrieved == pipeline);
        
        // 測試包含檢查
        m_framework.RunTest("檢查是否包含流水線", registry.Contains("DataFeed"));
        
        delete registry;
        delete pipeline;
    }
    
    // 測試流水線組
    void TestPipelineGroup()
    {
        Print("\n--- 測試流水線組 ---");
        
        PipelineGroup* group = new PipelineGroup("TestGroup", "測試組", TRADING_TICK);
        CompositePipeline* composite1 = new CompositePipeline("Composite1");
        CompositePipeline* composite2 = new CompositePipeline("Composite2");
        
        // 添加一些子流水線到複合流水線
        composite1.Add(new TestDataFeedPipeline());
        composite2.Add(new TestSignalPipeline());
        
        // 測試添加到組
        bool added1 = group.AddPipeline(composite1);
        bool added2 = group.AddPipeline(composite2);
        
        m_framework.RunTest("添加流水線到組", added1 && added2);
        m_framework.RunTest("組流水線數量檢查", group.GetPipelineCount() == 2);
        
        // 測試執行組
        bool success = group.ExecuteAll();
        m_framework.RunTest("執行流水線組", success);
        
        delete group;
        // composite1 和 composite2 會被 group 自動清理
    }
    
    // 測試模組管理器
    void TestModuleManager()
    {
        Print("\n--- 測試模組管理器 ---");
        
        // 測試初始化
        bool initialized = InitializeEAPipelineAdvance();
        m_framework.RunTest("模組管理器初始化", initialized);
        
        // 測試註冊流水線
        TestDataFeedPipeline* pipeline = new TestDataFeedPipeline();
        bool registered = RegisterTradingPipeline(pipeline, TRADING_TICK);
        m_framework.RunTest("註冊流水線到管理器", registered);
        
        // 測試執行事件
        bool executed = ExecuteTradingEvent(TRADING_TICK);
        m_framework.RunTest("執行交易事件", executed);
        
        // 測試獲取信息
        string version = GetEAPipelineAdvanceVersion();
        m_framework.RunTest("獲取模組版本", version == EAPIPELINE_ADVANCE_VERSION);
        
        delete pipeline;
    }
    
    // 測試錯誤處理
    void TestErrorHandling()
    {
        Print("\n--- 測試錯誤處理 ---");
        
        TestFailingPipeline* failingPipeline = new TestFailingPipeline();
        
        // 測試失敗流水線
        bool success = failingPipeline.Execute();
        m_framework.RunTest("失敗流水線執行結果", !success);
        m_framework.RunTest("失敗流水線狀態檢查", 
                           failingPipeline.GetStatus() == STATUS_FAILED && 
                           failingPipeline.HasError());
        
        // 測試錯誤信息
        string error = failingPipeline.GetLastError();
        m_framework.RunTest("獲取錯誤信息", error != "");
        
        // 測試複合流水線中的錯誤處理
        CompositePipeline* composite = new CompositePipeline("ErrorTestComposite", 10, EXECUTION_SEQUENTIAL, true);
        composite.Add(new TestDataFeedPipeline());
        composite.Add(failingPipeline);
        composite.Add(new TestSignalPipeline());
        
        bool compositeSuccess = composite.Execute();
        m_framework.RunTest("包含失敗流水線的複合流水線", !compositeSuccess);
        
        delete composite;
        delete failingPipeline;
    }
};

//+------------------------------------------------------------------+
//| 主測試函數                                                       |
//+------------------------------------------------------------------+
void RunEAPipelineAdvanceTests()
{
    EAPipelineAdvanceTest test;
    test.RunAllTests();
}
