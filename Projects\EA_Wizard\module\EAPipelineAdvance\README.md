# EAPipelineAdvance 模組

## 概述

EAPipelineAdvance 是對原始 EAPipeline 模組的重構版本，專注於簡化架構、提高可讀性和降低複雜度。本模組為 EA 交易系統提供了一個清晰、高效的流水線處理框架。

## 🎯 重構目標

### 原始模組的問題
- 過度複雜的裝飾者模式層次
- 命名不夠清晰（過多的 "EA" 前綴）
- 代碼重複和高耦合度
- 過多的單例模式使用

### 重構改進
- ✅ 簡化架構設計
- ✅ 使用清晰、有意義的命名
- ✅ 減少代碼重複
- ✅ 降低組件間耦合度
- ✅ 提高可測試性

## 📁 模組結構

```
EAPipelineAdvance/
├── EAPipelineAdvance.mqh          # 主入口文件
├── core/                          # 核心組件
│   ├── TradingPipeline.mqh       # 交易流水線基類
│   ├── PipelineExecutor.mqh      # 流水線執行器
│   └── TradingEvent.mqh          # 交易事件定義
├── composite/                     # 組合組件
│   ├── CompositePipeline.mqh     # 複合流水線
│   └── PipelineGroup.mqh         # 流水線組
├── features/                      # 功能組件
│   ├── LoggingPipeline.mqh       # 日誌流水線
│   ├── ErrorHandlingPipeline.mqh # 錯誤處理流水線
│   └── PipelineRegistry.mqh      # 流水線註冊器
├── utils/                         # 工具組件
│   ├── PipelineBuilder.mqh       # 流水線建造者
│   └── PipelineValidator.mqh     # 流水線驗證器
├── test/                          # 測試套件
│   ├── TestTradingPipeline.mqh   # 核心測試
│   ├── TestCompositePipeline.mqh # 組合測試
│   └── RunAllTests.mqh           # 測試運行器
└── docs/                          # 文檔
    ├── Architecture.md           # 架構文檔
    └── Examples.md               # 使用示例
```

## 🔧 核心組件

### 1. TradingPipeline
- 替代原始的 `EAPipeline`
- 提供清晰的交易流水線基礎功能
- 簡化的錯誤處理和狀態管理

### 2. PipelineExecutor
- 替代原始的 `EAPipelineManager`
- 專注於流水線的執行調度
- 支持同步和異步執行模式

### 3. CompositePipeline
- 替代原始的 `EACompoundPipeline`
- 使用組合模式管理子流水線
- 移除複雜的裝飾者模式

### 4. PipelineRegistry
- 替代原始的 `EARegistry`
- 簡化的鍵值存儲機制
- 類型安全的訪問方法

## 🚀 主要改進

### 命名改進
| 原始名稱 | 重構名稱 | 改進說明 |
|---------|---------|---------|
| `EAPipeline` | `TradingPipeline` | 更清晰的業務含義 |
| `EAPipelineManager` | `PipelineExecutor` | 專注執行職責 |
| `EACompoundPipeline` | `CompositePipeline` | 去除冗餘前綴 |
| `EALogPipeline` | `LoggingPipeline` | 更直觀的功能描述 |

### 架構簡化
- 減少繼承層次
- 使用組合替代複雜的裝飾者模式
- 移除不必要的抽象層
- 降低組件間依賴關係

### 代碼質量
- 提高可讀性
- 增強可測試性
- 改善可維護性
- 降低學習成本

## 📖 使用示例

```mql4
// 創建交易流水線
TradingPipeline* dataFeed = new DataFeedPipeline("市場數據");
TradingPipeline* signal = new SignalPipeline("交易信號");
TradingPipeline* order = new OrderPipeline("訂單處理");

// 創建複合流水線
CompositePipeline* mainPipeline = new CompositePipeline("主流水線");
mainPipeline.Add(dataFeed);
mainPipeline.Add(signal);
mainPipeline.Add(order);

// 執行流水線
PipelineExecutor* executor = new PipelineExecutor();
bool success = executor.Execute(mainPipeline);

if(success) {
    Print("流水線執行成功");
} else {
    Print("流水線執行失敗: ", executor.GetLastError());
}
```

## 🧪 測試

模組包含完整的測試套件，確保所有組件的正確性和穩定性。

```mql4
// 運行所有測試
#include "test/RunAllTests.mqh"

void OnStart() {
    RunAllEAPipelineAdvanceTests();
}
```

## 📚 文檔

- [架構文檔](docs/Architecture.md) - 詳細的架構設計說明
- [使用示例](docs/Examples.md) - 完整的使用示例和最佳實踐

## 🔄 遷移指南

從原始 EAPipeline 模組遷移到 EAPipelineAdvance：

1. 更新 include 路徑
2. 替換類名稱
3. 簡化初始化代碼
4. 移除不必要的裝飾者鏈

詳細的遷移步驟請參考 [遷移指南](docs/Migration.md)。

## 📈 性能改進

- 減少對象創建開銷
- 降低記憶體使用
- 提高執行效率
- 簡化調用鏈

## 🤝 貢獻

歡迎提交 Issue 和 Pull Request 來改進本模組。

## 📄 許可證

本模組遵循 EA_Wizard 專案的許可證。
