//+------------------------------------------------------------------+
//|                                                 TradingEvent.mqh |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

//+------------------------------------------------------------------+
//| 交易事件類型枚舉                                                 |
//+------------------------------------------------------------------+
enum ENUM_TRADING_EVENT
{
    TRADING_INIT,           // 初始化事件
    TRADING_TICK,           // 價格變動事件
    TRADING_DEINIT          // 清理事件
};

//+------------------------------------------------------------------+
//| 交易階段類型枚舉                                                 |
//+------------------------------------------------------------------+
enum ENUM_TRADING_STAGE
{
    // 初始化階段
    INIT_START,             // 初始化開始
    INIT_PARAMETERS,        // 參數設置
    INIT_VARIABLES,         // 變數初始化
    INIT_ENVIRONMENT,       // 環境檢查
    INIT_INDICATORS,        // 指標初始化
    INIT_COMPLETE,          // 初始化完成
    
    // 交易階段
    TICK_DATA_FEED,         // 數據饋送
    TICK_SIGNAL_ANALYSIS,   // 信號分析
    TICK_ORDER_MANAGEMENT,  // 訂單管理
    TICK_RISK_CONTROL,      // 風險控制
    TICK_LOGGING,           // 日誌記錄
    
    // 清理階段
    DEINIT_CLEANUP,         // 資源清理
    DEINIT_SAVE_STATE,      // 狀態保存
    DEINIT_COMPLETE         // 清理完成
};

//+------------------------------------------------------------------+
//| 流水線執行模式                                                   |
//+------------------------------------------------------------------+
enum ENUM_EXECUTION_MODE
{
    EXECUTION_SEQUENTIAL,   // 順序執行
    EXECUTION_PARALLEL,     // 並行執行
    EXECUTION_CONDITIONAL   // 條件執行
};

//+------------------------------------------------------------------+
//| 流水線狀態                                                       |
//+------------------------------------------------------------------+
enum ENUM_PIPELINE_STATUS
{
    STATUS_PENDING,         // 待執行
    STATUS_RUNNING,         // 執行中
    STATUS_COMPLETED,       // 已完成
    STATUS_FAILED,          // 執行失敗
    STATUS_CANCELLED        // 已取消
};

//+------------------------------------------------------------------+
//| 錯誤級別                                                         |
//+------------------------------------------------------------------+
enum ENUM_ERROR_LEVEL
{
    ERROR_LEVEL_INFO,       // 信息
    ERROR_LEVEL_WARNING,    // 警告
    ERROR_LEVEL_ERROR,      // 錯誤
    ERROR_LEVEL_CRITICAL    // 嚴重錯誤
};

//+------------------------------------------------------------------+
//| 常量定義                                                         |
//+------------------------------------------------------------------+
const string TRADING_PIPELINE_TYPE = "TradingPipeline";
const string COMPOSITE_PIPELINE_TYPE = "CompositePipeline";
const string LOGGING_PIPELINE_TYPE = "LoggingPipeline";
const string ERROR_HANDLING_PIPELINE_TYPE = "ErrorHandlingPipeline";

const int DEFAULT_MAX_PIPELINES = 100;
const int DEFAULT_MAX_RETRIES = 3;
const int DEFAULT_TIMEOUT_MS = 5000;

//+------------------------------------------------------------------+
//| 交易事件工具類                                                   |
//+------------------------------------------------------------------+
class TradingEventUtils
{
public:
    // 將事件轉換為字符串
    static string EventToString(ENUM_TRADING_EVENT event)
    {
        switch(event)
        {
            case TRADING_INIT:   return "INIT";
            case TRADING_TICK:   return "TICK";
            case TRADING_DEINIT: return "DEINIT";
            default:             return "UNKNOWN";
        }
    }
    
    // 將階段轉換為字符串
    static string StageToString(ENUM_TRADING_STAGE stage)
    {
        switch(stage)
        {
            case INIT_START:            return "INIT_START";
            case INIT_PARAMETERS:       return "INIT_PARAMETERS";
            case INIT_VARIABLES:        return "INIT_VARIABLES";
            case INIT_ENVIRONMENT:      return "INIT_ENVIRONMENT";
            case INIT_INDICATORS:       return "INIT_INDICATORS";
            case INIT_COMPLETE:         return "INIT_COMPLETE";
            
            case TICK_DATA_FEED:        return "TICK_DATA_FEED";
            case TICK_SIGNAL_ANALYSIS:  return "TICK_SIGNAL_ANALYSIS";
            case TICK_ORDER_MANAGEMENT: return "TICK_ORDER_MANAGEMENT";
            case TICK_RISK_CONTROL:     return "TICK_RISK_CONTROL";
            case TICK_LOGGING:          return "TICK_LOGGING";
            
            case DEINIT_CLEANUP:        return "DEINIT_CLEANUP";
            case DEINIT_SAVE_STATE:     return "DEINIT_SAVE_STATE";
            case DEINIT_COMPLETE:       return "DEINIT_COMPLETE";
            
            default:                    return "UNKNOWN_STAGE";
        }
    }
    
    // 將狀態轉換為字符串
    static string StatusToString(ENUM_PIPELINE_STATUS status)
    {
        switch(status)
        {
            case STATUS_PENDING:    return "PENDING";
            case STATUS_RUNNING:    return "RUNNING";
            case STATUS_COMPLETED:  return "COMPLETED";
            case STATUS_FAILED:     return "FAILED";
            case STATUS_CANCELLED:  return "CANCELLED";
            default:                return "UNKNOWN_STATUS";
        }
    }
    
    // 檢查事件是否有效
    static bool IsValidEvent(ENUM_TRADING_EVENT event)
    {
        return (event >= TRADING_INIT && event <= TRADING_DEINIT);
    }
    
    // 檢查階段是否屬於指定事件
    static bool IsStageOfEvent(ENUM_TRADING_STAGE stage, ENUM_TRADING_EVENT event)
    {
        switch(event)
        {
            case TRADING_INIT:
                return (stage >= INIT_START && stage <= INIT_COMPLETE);
                
            case TRADING_TICK:
                return (stage >= TICK_DATA_FEED && stage <= TICK_LOGGING);
                
            case TRADING_DEINIT:
                return (stage >= DEINIT_CLEANUP && stage <= DEINIT_COMPLETE);
                
            default:
                return false;
        }
    }
    
    // 獲取事件的所有階段
    static int GetEventStages(ENUM_TRADING_EVENT event, ENUM_TRADING_STAGE &stages[])
    {
        switch(event)
        {
            case TRADING_INIT:
                ArrayResize(stages, 6);
                stages[0] = INIT_START;
                stages[1] = INIT_PARAMETERS;
                stages[2] = INIT_VARIABLES;
                stages[3] = INIT_ENVIRONMENT;
                stages[4] = INIT_INDICATORS;
                stages[5] = INIT_COMPLETE;
                return 6;
                
            case TRADING_TICK:
                ArrayResize(stages, 5);
                stages[0] = TICK_DATA_FEED;
                stages[1] = TICK_SIGNAL_ANALYSIS;
                stages[2] = TICK_ORDER_MANAGEMENT;
                stages[3] = TICK_RISK_CONTROL;
                stages[4] = TICK_LOGGING;
                return 5;
                
            case TRADING_DEINIT:
                ArrayResize(stages, 3);
                stages[0] = DEINIT_CLEANUP;
                stages[1] = DEINIT_SAVE_STATE;
                stages[2] = DEINIT_COMPLETE;
                return 3;
                
            default:
                ArrayResize(stages, 0);
                return 0;
        }
    }
};
