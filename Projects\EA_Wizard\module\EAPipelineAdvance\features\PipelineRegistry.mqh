//+------------------------------------------------------------------+
//|                                           PipelineRegistry.mqh |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../core/TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 註冊項目                                                         |
//+------------------------------------------------------------------+
template<typename T>
class RegistryItem
{
private:
    string m_key;           // 鍵
    string m_description;   // 描述
    T m_value;              // 值
    datetime m_timestamp;   // 註冊時間戳

public:
    // 構造函數
    RegistryItem(string key, string description, T value)
        : m_key(key),
          m_description(description),
          m_value(value),
          m_timestamp(TimeCurrent())
    {
    }
    
    // 析構函數
    ~RegistryItem() {}
    
    // 獲取屬性
    string GetKey() const { return m_key; }
    string GetDescription() const { return m_description; }
    T GetValue() const { return m_value; }
    datetime GetTimestamp() const { return m_timestamp; }
    
    // 設置屬性
    void SetDescription(string description) { m_description = description; }
    void SetValue(T value) { m_value = value; }
    
    // 轉換為字符串
    string ToString() const
    {
        return StringFormat("Key: %s, Description: %s, Timestamp: %s",
                          m_key, m_description, TimeToString(m_timestamp));
    }
};

//+------------------------------------------------------------------+
//| 註冊結果                                                         |
//+------------------------------------------------------------------+
class RegistryResult
{
private:
    bool m_success;         // 是否成功
    string m_message;       // 結果消息
    string m_key;           // 相關鍵值
    datetime m_timestamp;   // 時間戳

public:
    // 構造函數
    RegistryResult(bool success, string message, string key = "")
        : m_success(success),
          m_message(message),
          m_key(key),
          m_timestamp(TimeCurrent())
    {
    }
    
    // 析構函數
    ~RegistryResult() {}
    
    // 獲取屬性
    bool IsSuccess() const { return m_success; }
    string GetMessage() const { return m_message; }
    string GetKey() const { return m_key; }
    datetime GetTimestamp() const { return m_timestamp; }
    
    // 轉換為字符串
    string ToString() const
    {
        return StringFormat("[%s] %s: %s (Key: %s)",
                          TimeToString(m_timestamp),
                          m_success ? "SUCCESS" : "FAILED",
                          m_message, m_key);
    }
};

//+------------------------------------------------------------------+
//| 流水線註冊器                                                     |
//+------------------------------------------------------------------+
template<typename T>
class PipelineRegistry
{
private:
    string m_name;                      // 註冊器名稱
    RegistryItem<T>* m_items[];         // 註冊項目數組
    int m_maxItems;                     // 最大項目數量
    bool m_allowOverwrite;              // 是否允許覆蓋

public:
    // 構造函數
    PipelineRegistry(string name, 
                    int maxItems = DEFAULT_MAX_PIPELINES,
                    bool allowOverwrite = false)
        : m_name(name),
          m_maxItems(maxItems),
          m_allowOverwrite(allowOverwrite)
    {
        ArrayResize(m_items, 0);
    }
    
    // 析構函數
    ~PipelineRegistry()
    {
        Clear();
    }
    
    // 註冊項目
    RegistryResult* Register(string key, string description, T value)
    {
        // 檢查鍵是否為空
        if(key == "")
        {
            return new RegistryResult(false, "鍵不能為空");
        }
        
        // 檢查是否已存在
        int existingIndex = FindIndex(key);
        if(existingIndex >= 0)
        {
            if(m_allowOverwrite)
            {
                // 覆蓋現有項目
                delete m_items[existingIndex];
                m_items[existingIndex] = new RegistryItem<T>(key, description, value);
                return new RegistryResult(true, "項目已更新", key);
            }
            else
            {
                return new RegistryResult(false, "鍵已存在", key);
            }
        }
        
        // 檢查是否達到最大數量
        if(ArraySize(m_items) >= m_maxItems)
        {
            return new RegistryResult(false, "已達到最大項目數量", key);
        }
        
        // 添加新項目
        int newSize = ArraySize(m_items) + 1;
        ArrayResize(m_items, newSize);
        m_items[newSize - 1] = new RegistryItem<T>(key, description, value);
        
        return new RegistryResult(true, "項目註冊成功", key);
    }
    
    // 獲取項目
    RegistryItem<T>* GetItem(string key)
    {
        int index = FindIndex(key);
        if(index >= 0)
        {
            return m_items[index];
        }
        return NULL;
    }
    
    // 獲取值
    T GetValue(string key, T defaultValue)
    {
        RegistryItem<T>* item = GetItem(key);
        if(item != NULL)
        {
            return item.GetValue();
        }
        return defaultValue;
    }
    
    // 移除項目
    bool Unregister(string key)
    {
        int index = FindIndex(key);
        if(index < 0)
        {
            return false;
        }
        
        // 刪除項目
        delete m_items[index];
        
        // 移動數組元素
        for(int i = index; i < ArraySize(m_items) - 1; i++)
        {
            m_items[i] = m_items[i + 1];
        }
        ArrayResize(m_items, ArraySize(m_items) - 1);
        
        return true;
    }
    
    // 檢查是否包含鍵
    bool Contains(string key)
    {
        return FindIndex(key) >= 0;
    }
    
    // 清空所有項目
    void Clear()
    {
        for(int i = 0; i < ArraySize(m_items); i++)
        {
            delete m_items[i];
        }
        ArrayResize(m_items, 0);
    }
    
    // 獲取所有鍵
    void GetAllKeys(string &keys[])
    {
        ArrayResize(keys, ArraySize(m_items));
        for(int i = 0; i < ArraySize(m_items); i++)
        {
            keys[i] = m_items[i].GetKey();
        }
    }
    
    // 獲取項目數量
    int GetCount() const { return ArraySize(m_items); }
    
    // 獲取最大項目數量
    int GetMaxItems() const { return m_maxItems; }
    
    // 獲取註冊器名稱
    string GetName() const { return m_name; }
    
    // 是否允許覆蓋
    bool IsOverwriteAllowed() const { return m_allowOverwrite; }
    void SetAllowOverwrite(bool allow) { m_allowOverwrite = allow; }
    
    // 獲取使用率
    double GetUsageRate() const
    {
        return m_maxItems > 0 ? (double)ArraySize(m_items) / m_maxItems * 100.0 : 0.0;
    }
    
    // 獲取狀態信息
    string GetStatusInfo() const
    {
        return StringFormat("註冊器 [%s]: 項目數=%d/%d (%.1f%%), 允許覆蓋=%s",
                          m_name, ArraySize(m_items), m_maxItems, GetUsageRate(),
                          m_allowOverwrite ? "是" : "否");
    }
    
    // 按索引獲取項目
    RegistryItem<T>* GetItemByIndex(int index)
    {
        if(index < 0 || index >= ArraySize(m_items))
        {
            return NULL;
        }
        return m_items[index];
    }

private:
    // 查找項目索引
    int FindIndex(string key)
    {
        for(int i = 0; i < ArraySize(m_items); i++)
        {
            if(m_items[i].GetKey() == key)
            {
                return i;
            }
        }
        return -1;
    }
};

//+------------------------------------------------------------------+
//| 流水線專用註冊器                                                 |
//+------------------------------------------------------------------+
class TradingPipelineRegistry : public PipelineRegistry<ITradingPipeline*>
{
public:
    // 構造函數
    TradingPipelineRegistry(string name = "TradingPipelineRegistry",
                           int maxItems = DEFAULT_MAX_PIPELINES)
        : PipelineRegistry<ITradingPipeline*>(name, maxItems, false)
    {
    }
    
    // 註冊流水線
    RegistryResult* RegisterPipeline(ITradingPipeline* pipeline)
    {
        if(pipeline == NULL)
        {
            return new RegistryResult(false, "流水線不能為空");
        }
        
        string key = pipeline.GetName();
        string description = "流水線: " + pipeline.GetType();
        
        return Register(key, description, pipeline);
    }
    
    // 按名稱獲取流水線
    ITradingPipeline* GetPipeline(string name)
    {
        return GetValue(name, NULL);
    }
    
    // 執行指定流水線
    bool ExecutePipeline(string name)
    {
        ITradingPipeline* pipeline = GetPipeline(name);
        if(pipeline == NULL)
        {
            Print("未找到流水線: ", name);
            return false;
        }
        
        return pipeline.Execute();
    }
    
    // 重置指定流水線
    bool ResetPipeline(string name)
    {
        ITradingPipeline* pipeline = GetPipeline(name);
        if(pipeline == NULL)
        {
            Print("未找到流水線: ", name);
            return false;
        }
        
        pipeline.Reset();
        return true;
    }
    
    // 重置所有流水線
    void ResetAllPipelines()
    {
        for(int i = 0; i < GetCount(); i++)
        {
            RegistryItem<ITradingPipeline*>* item = GetItemByIndex(i);
            if(item != NULL && item.GetValue() != NULL)
            {
                item.GetValue().Reset();
            }
        }
    }
};
