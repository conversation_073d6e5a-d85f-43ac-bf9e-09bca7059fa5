//+------------------------------------------------------------------+
//|                                             PipelineExecutor.mqh |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 執行統計信息                                                     |
//+------------------------------------------------------------------+
class ExecutionStats
{
private:
    int m_totalExecuted;        // 總執行數
    int m_successCount;         // 成功數
    int m_failureCount;         // 失敗數
    datetime m_startTime;       // 開始時間
    datetime m_endTime;         // 結束時間
    double m_totalDuration;     // 總耗時（秒）

public:
    // 構造函數
    ExecutionStats()
        : m_totalExecuted(0),
          m_successCount(0),
          m_failureCount(0),
          m_startTime(0),
          m_endTime(0),
          m_totalDuration(0.0)
    {
    }
    
    // 重置統計
    void Reset()
    {
        m_totalExecuted = 0;
        m_successCount = 0;
        m_failureCount = 0;
        m_startTime = 0;
        m_endTime = 0;
        m_totalDuration = 0.0;
    }
    
    // 開始執行
    void StartExecution()
    {
        m_startTime = TimeCurrent();
    }
    
    // 結束執行
    void EndExecution()
    {
        m_endTime = TimeCurrent();
        if(m_startTime > 0)
        {
            m_totalDuration = m_endTime - m_startTime;
        }
    }
    
    // 記錄執行結果
    void RecordExecution(bool success)
    {
        m_totalExecuted++;
        if(success)
        {
            m_successCount++;
        }
        else
        {
            m_failureCount++;
        }
    }
    
    // 獲取統計信息
    int GetTotalExecuted() const { return m_totalExecuted; }
    int GetSuccessCount() const { return m_successCount; }
    int GetFailureCount() const { return m_failureCount; }
    double GetSuccessRate() const 
    { 
        return m_totalExecuted > 0 ? (double)m_successCount / m_totalExecuted * 100.0 : 0.0; 
    }
    double GetDuration() const { return m_totalDuration; }
    
    // 轉換為字符串
    string ToString() const
    {
        return StringFormat("執行統計: 總數=%d, 成功=%d, 失敗=%d, 成功率=%.2f%%, 耗時=%.2f秒",
                          m_totalExecuted, m_successCount, m_failureCount, 
                          GetSuccessRate(), m_totalDuration);
    }
};

//+------------------------------------------------------------------+
//| 流水線執行器                                                     |
//+------------------------------------------------------------------+
class PipelineExecutor
{
private:
    string m_name;                      // 執行器名稱
    ENUM_EXECUTION_MODE m_mode;         // 執行模式
    bool m_stopOnError;                 // 遇到錯誤時是否停止
    int m_maxRetries;                   // 最大重試次數
    ExecutionStats m_stats;             // 執行統計
    string m_lastError;                 // 最後錯誤信息
    ITradingPipeline* m_pipelines[];    // 管理的流水線列表

public:
    // 構造函數
    PipelineExecutor(string name = "PipelineExecutor", 
                    ENUM_EXECUTION_MODE mode = EXECUTION_SEQUENTIAL,
                    bool stopOnError = true,
                    int maxRetries = DEFAULT_MAX_RETRIES)
        : m_name(name),
          m_mode(mode),
          m_stopOnError(stopOnError),
          m_maxRetries(maxRetries),
          m_lastError("")
    {
        ArrayResize(m_pipelines, 0);
    }
    
    // 析構函數
    ~PipelineExecutor()
    {
        Clear();
    }
    
    // 添加流水線
    bool AddPipeline(ITradingPipeline* pipeline)
    {
        if(pipeline == NULL)
        {
            m_lastError = "流水線不能為空";
            return false;
        }
        
        // 檢查是否已存在
        for(int i = 0; i < ArraySize(m_pipelines); i++)
        {
            if(m_pipelines[i] == pipeline)
            {
                m_lastError = "流水線已存在";
                return false;
            }
        }
        
        // 添加到數組
        int newSize = ArraySize(m_pipelines) + 1;
        ArrayResize(m_pipelines, newSize);
        m_pipelines[newSize - 1] = pipeline;
        
        return true;
    }
    
    // 移除流水線
    bool RemovePipeline(ITradingPipeline* pipeline)
    {
        if(pipeline == NULL)
        {
            m_lastError = "流水線不能為空";
            return false;
        }
        
        int index = -1;
        for(int i = 0; i < ArraySize(m_pipelines); i++)
        {
            if(m_pipelines[i] == pipeline)
            {
                index = i;
                break;
            }
        }
        
        if(index == -1)
        {
            m_lastError = "流水線不存在";
            return false;
        }
        
        // 移除元素
        for(int i = index; i < ArraySize(m_pipelines) - 1; i++)
        {
            m_pipelines[i] = m_pipelines[i + 1];
        }
        ArrayResize(m_pipelines, ArraySize(m_pipelines) - 1);
        
        return true;
    }
    
    // 執行單個流水線
    bool Execute(ITradingPipeline* pipeline)
    {
        if(pipeline == NULL)
        {
            m_lastError = "流水線不能為空";
            return false;
        }
        
        m_stats.StartExecution();
        
        bool success = ExecuteWithRetry(pipeline);
        
        m_stats.EndExecution();
        m_stats.RecordExecution(success);
        
        if(!success)
        {
            m_lastError = pipeline.GetLastError();
        }
        
        return success;
    }
    
    // 執行所有流水線
    bool ExecuteAll()
    {
        if(ArraySize(m_pipelines) == 0)
        {
            m_lastError = "沒有流水線可執行";
            return false;
        }
        
        m_stats.Reset();
        m_stats.StartExecution();
        
        bool overallSuccess = true;
        
        for(int i = 0; i < ArraySize(m_pipelines); i++)
        {
            bool success = ExecuteWithRetry(m_pipelines[i]);
            m_stats.RecordExecution(success);
            
            if(!success)
            {
                overallSuccess = false;
                m_lastError = m_pipelines[i].GetLastError();
                
                if(m_stopOnError)
                {
                    Print("執行器 [", m_name, "] 在流水線 [", 
                          m_pipelines[i].GetName(), "] 處停止，錯誤: ", m_lastError);
                    break;
                }
            }
        }
        
        m_stats.EndExecution();
        
        Print("執行器 [", m_name, "] 完成執行: ", m_stats.ToString());
        
        return overallSuccess;
    }
    
    // 清空所有流水線
    void Clear()
    {
        ArrayResize(m_pipelines, 0);
        m_stats.Reset();
        m_lastError = "";
    }
    
    // 重置所有流水線
    void ResetAll()
    {
        for(int i = 0; i < ArraySize(m_pipelines); i++)
        {
            m_pipelines[i].Reset();
        }
        m_stats.Reset();
        m_lastError = "";
    }
    
    // 獲取流水線數量
    int GetPipelineCount() const { return ArraySize(m_pipelines); }
    
    // 獲取執行統計
    ExecutionStats* GetStats() { return &m_stats; }
    
    // 獲取最後錯誤
    string GetLastError() const { return m_lastError; }
    
    // 設置執行模式
    void SetExecutionMode(ENUM_EXECUTION_MODE mode) { m_mode = mode; }
    ENUM_EXECUTION_MODE GetExecutionMode() const { return m_mode; }
    
    // 設置錯誤處理策略
    void SetStopOnError(bool stopOnError) { m_stopOnError = stopOnError; }
    bool GetStopOnError() const { return m_stopOnError; }
    
    // 設置最大重試次數
    void SetMaxRetries(int maxRetries) { m_maxRetries = maxRetries; }
    int GetMaxRetries() const { return m_maxRetries; }

private:
    // 帶重試的執行
    bool ExecuteWithRetry(ITradingPipeline* pipeline)
    {
        int attempts = 0;
        bool success = false;
        
        while(attempts <= m_maxRetries && !success)
        {
            attempts++;
            
            if(attempts > 1)
            {
                Print("重試執行流水線 [", pipeline.GetName(), "] 第 ", attempts, " 次");
                pipeline.Reset(); // 重置狀態
            }
            
            success = pipeline.Execute();
            
            if(!success && attempts <= m_maxRetries)
            {
                Print("流水線 [", pipeline.GetName(), "] 執行失敗: ", 
                      pipeline.GetLastError(), " (嘗試 ", attempts, "/", m_maxRetries + 1, ")");
            }
        }
        
        if(success)
        {
            Print("流水線 [", pipeline.GetName(), "] 執行成功");
        }
        else
        {
            Print("流水線 [", pipeline.GetName(), "] 執行失敗，已達最大重試次數");
        }
        
        return success;
    }
};
