//+------------------------------------------------------------------+
//|                                                  ProgramBase.mqh |
//|                                    EA_Wizard 模組化程序系統      |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "ProgramType.mqh"

// 前向聲明
class ProgramManager;

//+------------------------------------------------------------------+
//| 程序基類 - 所有程序模組的抽象基類                                  |
//| 提供統一的程序介面和自動註冊機制                                   |
//+------------------------------------------------------------------+
class ProgramBase
{
protected:
    string           m_name;        // 程序名稱
    ProgramType      m_type;        // 程序類型（執行階段）
    ProgramManager*  m_manager;     // 程序管理器指標
    bool            m_initialized;  // 是否已初始化
    bool            m_enabled;      // 是否啟用

public:
    //+------------------------------------------------------------------+
    //| 構造函數                                                          |
    //| 參數:                                                            |
    //|   name - 程序名稱                                               |
    //|   type - 程序類型                                               |
    //|   manager - 程序管理器指標                                       |
    //+------------------------------------------------------------------+
    ProgramBase(string name, ProgramType type, ProgramManager* manager = NULL);

    //+------------------------------------------------------------------+
    //| 析構函數                                                          |
    //+------------------------------------------------------------------+
    virtual ~ProgramBase();

    //+------------------------------------------------------------------+
    //| 純虛函數 - 執行程序邏輯                                           |
    //| 子類必須實現此方法                                               |
    //| 返回值: 執行是否成功                                             |
    //+------------------------------------------------------------------+
    virtual bool Execute() = 0;

    //+------------------------------------------------------------------+
    //| 虛函數 - 初始化程序                                              |
    //| 子類可以重寫此方法進行自定義初始化                                |
    //| 返回值: 初始化是否成功                                           |
    //+------------------------------------------------------------------+
    virtual bool Initialize() { m_initialized = true; return true; }

    //+------------------------------------------------------------------+
    //| 虛函數 - 終止程序                                                |
    //| 子類可以重寫此方法進行清理工作                                    |
    //+------------------------------------------------------------------+
    virtual void Deinitialize() { m_initialized = false; }

    //+------------------------------------------------------------------+
    //| 獲取程序名稱                                                      |
    //| 返回值: 程序名稱                                                 |
    //+------------------------------------------------------------------+
    string GetName() const { return m_name; }

    //+------------------------------------------------------------------+
    //| 獲取程序類型                                                      |
    //| 返回值: 程序類型                                                 |
    //+------------------------------------------------------------------+
    ProgramType GetType() const { return m_type; }

    //+------------------------------------------------------------------+
    //| 獲取程序管理器                                                    |
    //| 返回值: 程序管理器指標                                           |
    //+------------------------------------------------------------------+
    ProgramManager* GetManager() const { return m_manager; }

    //+------------------------------------------------------------------+
    //| 設置程序管理器                                                    |
    //| 參數:                                                            |
    //|   manager - 程序管理器指標                                       |
    //+------------------------------------------------------------------+
    void SetManager(ProgramManager* manager) { m_manager = manager; }

    //+------------------------------------------------------------------+
    //| 檢查程序是否已初始化                                              |
    //| 返回值: 如果已初始化返回 true                                     |
    //+------------------------------------------------------------------+
    bool IsInitialized() const { return m_initialized; }

    //+------------------------------------------------------------------+
    //| 檢查程序是否啟用                                                  |
    //| 返回值: 如果啟用返回 true                                         |
    //+------------------------------------------------------------------+
    bool IsEnabled() const { return m_enabled; }

    //+------------------------------------------------------------------+
    //| 設置程序啟用狀態                                                  |
    //| 參數:                                                            |
    //|   enabled - 是否啟用                                            |
    //+------------------------------------------------------------------+
    void SetEnabled(bool enabled) { m_enabled = enabled; }

    //+------------------------------------------------------------------+
    //| 獲取程序信息字符串                                                |
    //| 返回值: 包含程序詳細信息的字符串                                   |
    //+------------------------------------------------------------------+
    virtual string ToString() const;

protected:
    //+------------------------------------------------------------------+
    //| 自動註冊到程序管理器                                              |
    //| 在構造函數中調用，實現自動註冊機制                                |
    //+------------------------------------------------------------------+
    void AutoRegister();

    //+------------------------------------------------------------------+
    //| 記錄日誌信息                                                      |
    //| 參數:                                                            |
    //|   message - 日誌消息                                            |
    //+------------------------------------------------------------------+
    void LogInfo(string message);

    //+------------------------------------------------------------------+
    //| 記錄錯誤信息                                                      |
    //| 參數:                                                            |
    //|   message - 錯誤消息                                            |
    //+------------------------------------------------------------------+
    void LogError(string message);

    //+------------------------------------------------------------------+
    //| 記錄警告信息                                                      |
    //| 參數:                                                            |
    //|   message - 警告消息                                            |
    //+------------------------------------------------------------------+
    void LogWarning(string message);
};

//+------------------------------------------------------------------+
//| ProgramBase 類實現                                                |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 構造函數實現                                                      |
//+------------------------------------------------------------------+
ProgramBase::ProgramBase(string name, ProgramType type, ProgramManager* manager = NULL)
    : m_name(name),
      m_type(type),
      m_manager(manager),
      m_initialized(false),
      m_enabled(true)
{
    // 驗證程序類型
    if(!IsValidProgramType(type))
    {
        LogError("無效的程序類型: " + IntegerToString(type));
        m_type = PROGRAM_TYPE_TICK; // 設置為默認類型
    }

    // 自動註冊到管理器
    AutoRegister();

    LogInfo("程序已創建: " + m_name + " [" + ProgramTypeToString(m_type) + "]");
}

//+------------------------------------------------------------------+
//| 析構函數實現                                                      |
//+------------------------------------------------------------------+
ProgramBase::~ProgramBase()
{
    LogInfo("程序正在銷毀: " + m_name);

    // 如果已初始化，先進行終止
    if(m_initialized)
    {
        Deinitialize();
    }
}

//+------------------------------------------------------------------+
//| 獲取程序信息字符串實現                                            |
//+------------------------------------------------------------------+
string ProgramBase::ToString() const
{
    return StringFormat("%s [%s] - 初始化: %s, 啟用: %s",
                      m_name,
                      ProgramTypeToString(m_type),
                      m_initialized ? "是" : "否",
                      m_enabled ? "是" : "否");
}

//+------------------------------------------------------------------+
//| 自動註冊實現                                                      |
//+------------------------------------------------------------------+
void ProgramBase::AutoRegister()
{
    // 自動註冊功能將在程序管理器可用時執行
    // 這裡暫時只記錄日誌，實際註冊將由外部調用
    LogInfo("程序準備註冊: " + m_name);
}

//+------------------------------------------------------------------+
//| 記錄日誌信息實現                                                  |
//+------------------------------------------------------------------+
void ProgramBase::LogInfo(string message)
{
    Print("INFO [", m_name, "]: ", message);
}

//+------------------------------------------------------------------+
//| 記錄錯誤信息實現                                                  |
//+------------------------------------------------------------------+
void ProgramBase::LogError(string message)
{
    Print("ERROR [", m_name, "]: ", message);
}

//+------------------------------------------------------------------+
//| 記錄警告信息實現                                                  |
//+------------------------------------------------------------------+
void ProgramBase::LogWarning(string message)
{
    Print("WARNING [", m_name, "]: ", message);
}
