//+------------------------------------------------------------------+
//|                                               ProgramManager.mqh |
//|                                    EA_Wizard 模組化程序系統      |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include <Arrays\ArrayObj.mqh>
#include "ProgramType.mqh"
#include "ProgramBase.mqh"

//+------------------------------------------------------------------+
//| 程序管理器類                                                      |
//| 負責管理和執行所有註冊的程序模組                                   |
//+------------------------------------------------------------------+
class ProgramManager
{
private:
    CArrayObj*      m_initPrograms;     // 初始化階段程序列表
    CArrayObj*      m_tickPrograms;     // 週期執行階段程序列表
    CArrayObj*      m_deinitPrograms;   // 終止階段程序列表
    bool           m_initialized;       // 管理器是否已初始化
    static ProgramManager* s_instance;  // 單例實例

public:
    //+------------------------------------------------------------------+
    //| 構造函數                                                          |
    //+------------------------------------------------------------------+
    ProgramManager();

    //+------------------------------------------------------------------+
    //| 析構函數                                                          |
    //+------------------------------------------------------------------+
    ~ProgramManager();

    //+------------------------------------------------------------------+
    //| 獲取單例實例                                                      |
    //| 返回值: ProgramManager 單例實例                                   |
    //+------------------------------------------------------------------+
    static ProgramManager* GetInstance();

    //+------------------------------------------------------------------+
    //| 釋放單例實例                                                      |
    //+------------------------------------------------------------------+
    static void ReleaseInstance();

    //+------------------------------------------------------------------+
    //| 初始化管理器                                                      |
    //| 返回值: 初始化是否成功                                           |
    //+------------------------------------------------------------------+
    bool Initialize();

    //+------------------------------------------------------------------+
    //| 終止管理器                                                        |
    //+------------------------------------------------------------------+
    void Deinitialize();

    //+------------------------------------------------------------------+
    //| 註冊程序模組                                                      |
    //| 參數:                                                            |
    //|   program - 要註冊的程序指標                                     |
    //| 返回值: 註冊是否成功                                             |
    //+------------------------------------------------------------------+
    bool Register(ProgramBase* program);

    //+------------------------------------------------------------------+
    //| 取消註冊程序模組                                                  |
    //| 參數:                                                            |
    //|   program - 要取消註冊的程序指標                                 |
    //| 返回值: 取消註冊是否成功                                         |
    //+------------------------------------------------------------------+
    bool Unregister(ProgramBase* program);

    //+------------------------------------------------------------------+
    //| 執行初始化階段程序                                                |
    //| 返回值: 執行結果代碼                                             |
    //+------------------------------------------------------------------+
    int RunInit();

    //+------------------------------------------------------------------+
    //| 執行週期階段程序                                                  |
    //| 返回值: 執行是否成功                                             |
    //+------------------------------------------------------------------+
    bool RunTick();

    //+------------------------------------------------------------------+
    //| 執行終止階段程序                                                  |
    //| 參數:                                                            |
    //|   reason - 終止原因                                             |
    //+------------------------------------------------------------------+
    void RunDeinit(int reason = 0);

    //+------------------------------------------------------------------+
    //| 獲取指定類型的程序數量                                            |
    //| 參數:                                                            |
    //|   type - 程序類型                                               |
    //| 返回值: 程序數量                                                 |
    //+------------------------------------------------------------------+
    int GetProgramCount(ProgramType type);

    //+------------------------------------------------------------------+
    //| 獲取所有程序總數                                                  |
    //| 返回值: 程序總數                                                 |
    //+------------------------------------------------------------------+
    int GetTotalProgramCount();

    //+------------------------------------------------------------------+
    //| 檢查管理器是否已初始化                                            |
    //| 返回值: 如果已初始化返回 true                                     |
    //+------------------------------------------------------------------+
    bool IsInitialized() const { return m_initialized; }

    //+------------------------------------------------------------------+
    //| 打印所有註冊的程序信息                                            |
    //+------------------------------------------------------------------+
    void PrintProgramInfo();

private:
    //+------------------------------------------------------------------+
    //| 根據程序類型獲取對應的程序列表                                    |
    //| 參數:                                                            |
    //|   type - 程序類型                                               |
    //| 返回值: 對應的程序列表指標                                       |
    //+------------------------------------------------------------------+
    CArrayObj* GetProgramList(ProgramType type);

    //+------------------------------------------------------------------+
    //| 執行指定列表中的所有程序                                          |
    //| 參數:                                                            |
    //|   programs - 程序列表                                           |
    //|   stageName - 階段名稱（用於日誌）                               |
    //| 返回值: 執行是否成功                                             |
    //+------------------------------------------------------------------+
    bool ExecutePrograms(CArrayObj* programs, string stageName);

    //+------------------------------------------------------------------+
    //| 記錄日誌信息                                                      |
    //| 參數:                                                            |
    //|   message - 日誌消息                                            |
    //+------------------------------------------------------------------+
    void LogInfo(string message);

    //+------------------------------------------------------------------+
    //| 記錄錯誤信息                                                      |
    //| 參數:                                                            |
    //|   message - 錯誤消息                                            |
    //+------------------------------------------------------------------+
    void LogError(string message);

    //+------------------------------------------------------------------+
    //| 記錄警告信息                                                      |
    //| 參數:                                                            |
    //|   message - 警告消息                                            |
    //+------------------------------------------------------------------+
    void LogWarning(string message);

    //+------------------------------------------------------------------+
    //| 打印程序列表信息                                                  |
    //| 參數:                                                            |
    //|   programs - 程序列表                                           |
    //|   listName - 列表名稱                                           |
    //+------------------------------------------------------------------+
    void PrintProgramListInfo(CArrayObj* programs, string listName);
};

//+------------------------------------------------------------------+
//| 靜態成員初始化                                                    |
//+------------------------------------------------------------------+
static ProgramManager* ProgramManager::s_instance = NULL;

//+------------------------------------------------------------------+
//| ProgramManager 類實現                                             |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 構造函數實現                                                      |
//+------------------------------------------------------------------+
ProgramManager::ProgramManager()
    : m_initPrograms(NULL),
      m_tickPrograms(NULL),
      m_deinitPrograms(NULL),
      m_initialized(false)
{
    LogInfo("程序管理器已創建");
}

//+------------------------------------------------------------------+
//| 析構函數實現                                                      |
//+------------------------------------------------------------------+
ProgramManager::~ProgramManager()
{
    LogInfo("程序管理器正在銷毀");
    Deinitialize();
}

//+------------------------------------------------------------------+
//| 獲取單例實例實現                                                  |
//+------------------------------------------------------------------+
ProgramManager* ProgramManager::GetInstance()
{
    if(s_instance == NULL)
    {
        s_instance = new ProgramManager();
        if(s_instance != NULL)
        {
            s_instance.Initialize();
        }
    }
    return s_instance;
}

//+------------------------------------------------------------------+
//| 釋放單例實例實現                                                  |
//+------------------------------------------------------------------+
void ProgramManager::ReleaseInstance()
{
    if(s_instance != NULL)
    {
        delete s_instance;
        s_instance = NULL;
    }
}

//+------------------------------------------------------------------+
//| 初始化管理器實現                                                  |
//+------------------------------------------------------------------+
bool ProgramManager::Initialize()
{
    if(m_initialized)
    {
        LogWarning("程序管理器已經初始化");
        return true;
    }

    // 創建程序列表
    m_initPrograms = new CArrayObj();
    m_tickPrograms = new CArrayObj();
    m_deinitPrograms = new CArrayObj();

    if(m_initPrograms == NULL || m_tickPrograms == NULL || m_deinitPrograms == NULL)
    {
        LogError("創建程序列表失敗");
        return false;
    }

    m_initialized = true;
    LogInfo("程序管理器初始化成功");
    return true;
}

//+------------------------------------------------------------------+
//| 終止管理器實現                                                    |
//+------------------------------------------------------------------+
void ProgramManager::Deinitialize()
{
    if(!m_initialized)
        return;

    LogInfo("程序管理器開始終止");

    // 清理程序列表
    if(m_initPrograms != NULL)
    {
        delete m_initPrograms;
        m_initPrograms = NULL;
    }

    if(m_tickPrograms != NULL)
    {
        delete m_tickPrograms;
        m_tickPrograms = NULL;
    }

    if(m_deinitPrograms != NULL)
    {
        delete m_deinitPrograms;
        m_deinitPrograms = NULL;
    }

    m_initialized = false;
    LogInfo("程序管理器終止完成");
}

//+------------------------------------------------------------------+
//| 註冊程序模組實現                                                  |
//+------------------------------------------------------------------+
bool ProgramManager::Register(ProgramBase* program)
{
    if(program == NULL)
    {
        LogError("嘗試註冊空程序指標");
        return false;
    }

    if(!m_initialized)
    {
        LogError("程序管理器未初始化，無法註冊程序: " + program.GetName());
        return false;
    }

    CArrayObj* targetList = GetProgramList(program.GetType());
    if(targetList == NULL)
    {
        LogError("無法獲取程序列表，類型: " + ProgramTypeToString(program.GetType()));
        return false;
    }

    // 檢查是否已經註冊
    for(int i = 0; i < targetList.Total(); i++)
    {
        ProgramBase* existingProgram = targetList.At(i);
        if(existingProgram == program)
        {
            LogWarning("程序已經註冊: " + program.GetName());
            return true;
        }
    }

    // 添加到列表
    if(targetList.Add(program))
    {
        LogInfo("程序註冊成功: " + program.GetName() + " [" + ProgramTypeToString(program.GetType()) + "]");
        return true;
    }
    else
    {
        LogError("程序註冊失敗: " + program.GetName());
        return false;
    }
}

//+------------------------------------------------------------------+
//| 取消註冊程序模組實現                                              |
//+------------------------------------------------------------------+
bool ProgramManager::Unregister(ProgramBase* program)
{
    if(program == NULL)
    {
        LogError("嘗試取消註冊空程序指標");
        return false;
    }

    if(!m_initialized)
    {
        LogError("程序管理器未初始化，無法取消註冊程序: " + program.GetName());
        return false;
    }

    CArrayObj* targetList = GetProgramList(program.GetType());
    if(targetList == NULL)
    {
        LogError("無法獲取程序列表，類型: " + ProgramTypeToString(program.GetType()));
        return false;
    }

    // 查找並移除程序
    for(int i = 0; i < targetList.Total(); i++)
    {
        ProgramBase* existingProgram = targetList.At(i);
        if(existingProgram == program)
        {
            targetList.Delete(i);
            LogInfo("程序取消註冊成功: " + program.GetName());
            return true;
        }
    }

    LogWarning("程序未找到，無法取消註冊: " + program.GetName());
    return false;
}

//+------------------------------------------------------------------+
//| 執行初始化階段程序實現                                            |
//+------------------------------------------------------------------+
int ProgramManager::RunInit()
{
    if(!m_initialized)
    {
        LogError("程序管理器未初始化，無法執行初始化階段");
        return INIT_FAILED;
    }

    LogInfo("開始執行初始化階段程序");

    if(!ExecutePrograms(m_initPrograms, "初始化"))
    {
        LogError("初始化階段程序執行失敗");
        return INIT_FAILED;
    }

    LogInfo("初始化階段程序執行完成");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| 執行週期階段程序實現                                              |
//+------------------------------------------------------------------+
bool ProgramManager::RunTick()
{
    if(!m_initialized)
    {
        LogError("程序管理器未初始化，無法執行週期階段");
        return false;
    }

    return ExecutePrograms(m_tickPrograms, "週期");
}

//+------------------------------------------------------------------+
//| 執行終止階段程序實現                                              |
//+------------------------------------------------------------------+
void ProgramManager::RunDeinit(int reason = 0)
{
    if(!m_initialized)
    {
        LogError("程序管理器未初始化，無法執行終止階段");
        return;
    }

    LogInfo("開始執行終止階段程序，原因: " + IntegerToString(reason));
    ExecutePrograms(m_deinitPrograms, "終止");
    LogInfo("終止階段程序執行完成");
}

//+------------------------------------------------------------------+
//| 獲取指定類型的程序數量實現                                        |
//+------------------------------------------------------------------+
int ProgramManager::GetProgramCount(ProgramType type)
{
    CArrayObj* targetList = GetProgramList(type);
    return (targetList != NULL) ? targetList.Total() : 0;
}

//+------------------------------------------------------------------+
//| 獲取所有程序總數實現                                              |
//+------------------------------------------------------------------+
int ProgramManager::GetTotalProgramCount()
{
    return GetProgramCount(PROGRAM_TYPE_INIT) +
           GetProgramCount(PROGRAM_TYPE_TICK) +
           GetProgramCount(PROGRAM_TYPE_DEINIT);
}

//+------------------------------------------------------------------+
//| 打印所有註冊的程序信息實現                                        |
//+------------------------------------------------------------------+
void ProgramManager::PrintProgramInfo()
{
    LogInfo("=== 程序管理器狀態信息 ===");
    LogInfo("初始化程序數量: " + IntegerToString(GetProgramCount(PROGRAM_TYPE_INIT)));
    LogInfo("週期程序數量: " + IntegerToString(GetProgramCount(PROGRAM_TYPE_TICK)));
    LogInfo("終止程序數量: " + IntegerToString(GetProgramCount(PROGRAM_TYPE_DEINIT)));
    LogInfo("程序總數: " + IntegerToString(GetTotalProgramCount()));

    // 打印每個程序的詳細信息
    PrintProgramListInfo(m_initPrograms, "初始化程序");
    PrintProgramListInfo(m_tickPrograms, "週期程序");
    PrintProgramListInfo(m_deinitPrograms, "終止程序");
}

//+------------------------------------------------------------------+
//| 根據程序類型獲取對應的程序列表實現                                |
//+------------------------------------------------------------------+
CArrayObj* ProgramManager::GetProgramList(ProgramType type)
{
    switch(type)
    {
        case PROGRAM_TYPE_INIT:   return m_initPrograms;
        case PROGRAM_TYPE_TICK:   return m_tickPrograms;
        case PROGRAM_TYPE_DEINIT: return m_deinitPrograms;
        default:                  return NULL;
    }
}

//+------------------------------------------------------------------+
//| 執行指定列表中的所有程序實現                                      |
//+------------------------------------------------------------------+
bool ProgramManager::ExecutePrograms(CArrayObj* programs, string stageName)
{
    if(programs == NULL)
    {
        LogWarning(stageName + "程序列表為空");
        return true;
    }

    int totalPrograms = programs.Total();
    if(totalPrograms == 0)
    {
        return true; // 沒有程序需要執行，視為成功
    }

    int successCount = 0;
    int failureCount = 0;

    for(int i = 0; i < totalPrograms; i++)
    {
        ProgramBase* program = programs.At(i);
        if(program == NULL)
        {
            LogError(stageName + "程序列表中存在空指標，索引: " + IntegerToString(i));
            failureCount++;
            continue;
        }

        if(!program.IsEnabled())
        {
            continue; // 跳過未啟用的程序
        }

        try
        {
            if(program.Execute())
            {
                successCount++;
            }
            else
            {
                LogError(stageName + "程序執行失敗: " + program.GetName());
                failureCount++;
            }
        }
        catch(string error)
        {
            LogError(stageName + "程序執行異常: " + program.GetName() + ", 錯誤: " + error);
            failureCount++;
        }
    }

    LogInfo(stageName + "階段執行完成 - 成功: " + IntegerToString(successCount) +
            ", 失敗: " + IntegerToString(failureCount));

    return (failureCount == 0);
}

//+------------------------------------------------------------------+
//| 打印程序列表信息                                                  |
//+------------------------------------------------------------------+
void ProgramManager::PrintProgramListInfo(CArrayObj* programs, string listName)
{
    if(programs == NULL)
        return;

    LogInfo("--- " + listName + " ---");
    for(int i = 0; i < programs.Total(); i++)
    {
        ProgramBase* program = programs.At(i);
        if(program != NULL)
        {
            LogInfo("  " + IntegerToString(i+1) + ". " + program.ToString());
        }
    }
}

//+------------------------------------------------------------------+
//| 記錄日誌信息實現                                                  |
//+------------------------------------------------------------------+
void ProgramManager::LogInfo(string message)
{
    Print("INFO [ProgramManager]: ", message);
}

//+------------------------------------------------------------------+
//| 記錄錯誤信息實現                                                  |
//+------------------------------------------------------------------+
void ProgramManager::LogError(string message)
{
    Print("ERROR [ProgramManager]: ", message);
}

//+------------------------------------------------------------------+
//| 記錄警告信息實現                                                  |
//+------------------------------------------------------------------+
void ProgramManager::LogWarning(string message)
{
    Print("WARNING [ProgramManager]: ", message);
}
