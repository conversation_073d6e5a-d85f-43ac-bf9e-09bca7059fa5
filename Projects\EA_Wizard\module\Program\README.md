# 📄 EA_Wizard 模組化程序系統

## 🎯 概述

EA_Wizard 模組化程序系統是一套完整的 MQL4 程式框架，實現了基於 mermaid 類圖設計的模組化架構。該系統提供了程式模組的集中管理與自動註冊機制，讓開發者能夠以高效率方式撰寫交易邏輯。

## 🏗️ 系統架構

### 核心組件

1. **ProgramType.mqh** - 程序類型枚舉定義
2. **ProgramBase.mqh** - 抽象基類，所有程序模組的基礎
3. **ProgramManager.mqh** - 程序管理器，負責註冊和執行模組
4. **MainController.mqh** - 主控制器，對接 EA 核心函數
5. **CustomProgramA.mqh** - 範例用戶定義類

### 類別關係圖

```mermaid
classDiagram
    class ProgramBase {
        <<abstract>>
        - name: string
        - type: ProgramType
        - manager: ProgramManager*
        + Execute(): void
        + GetType(): ProgramType
        + constructor(...)
    }

    class CustomProgramA {
        <<user-defined>>
        + Execute(): void
    }

    class ProgramManager {
        - initPrograms: CArrayObj
        - tickPrograms: CArrayObj
        - deinitPrograms: CArrayObj
        + Register(p: ProgramBase*)
        + RunInit()
        + RunTick()
        + RunDeinit()
    }

    class MainController {
        - manager: ProgramManager
        + OnInit()
        + OnTick()
        + OnDeinit()
    }

    class ProgramType {
        <<enumeration>>
        PROGRAM_TYPE_INIT
        PROGRAM_TYPE_TICK
        PROGRAM_TYPE_DEINIT
    }

    ProgramBase <|-- CustomProgramA
    ProgramBase "1" o-- "0..*" ProgramManager : manager
    ProgramManager "1" o-- "0..*" ProgramBase : manages
    MainController "1" o-- "1" ProgramManager : manager
    ProgramBase --> ProgramType : type
```

## 🚀 快速開始

### 1. 基本使用

```mql4
#include "ProgramExample.mqh"

// 全局控制器實例
ExampleController g_controller;

int OnInit()    { return g_controller.OnInit(); }
void OnTick()   { g_controller.OnTick(); }
void OnDeinit(const int reason) { g_controller.OnDeinit(reason); }
```

### 2. 創建自定義程序模組

```mql4
class MyCustomProgram : public ProgramBase
{
public:
    MyCustomProgram() : ProgramBase("MyProgram", PROGRAM_TYPE_TICK, ProgramManager::GetInstance())
    {
    }
    
    virtual bool Execute() override
    {
        // 實現您的邏輯
        LogInfo("我的自定義程序正在執行");
        return true;
    }
};
```

### 3. 註冊程序模組

```mql4
void CreateMyPrograms()
{
    ProgramManager* manager = ProgramManager::GetInstance();
    
    MyCustomProgram* myProgram = new MyCustomProgram();
    manager.Register(myProgram);
}
```

## 📋 程序類型

系統支援三種程序類型：

- **PROGRAM_TYPE_INIT** - 在 OnInit() 階段執行
- **PROGRAM_TYPE_TICK** - 在 OnTick() 階段執行  
- **PROGRAM_TYPE_DEINIT** - 在 OnDeinit() 階段執行

## 🔧 核心功能

### ProgramManager 功能

- ✅ 程序模組註冊與取消註冊
- ✅ 分階段執行程序（Init/Tick/Deinit）
- ✅ 程序狀態管理
- ✅ 錯誤處理與日誌記錄
- ✅ 程序統計與狀態報告

### ProgramBase 功能

- ✅ 統一的程序介面
- ✅ 自動生命週期管理
- ✅ 啟用/禁用控制
- ✅ 日誌記錄功能
- ✅ 程序信息查詢

### MainController 功能

- ✅ EA 生命週期管理
- ✅ 程序管理器整合
- ✅ 狀態監控與報告
- ✅ 錯誤處理
- ✅ 運行時間統計

## 📁 文件結構

```
Program/
├── ProgramType.mqh          # 程序類型枚舉
├── ProgramBase.mqh          # 抽象基類
├── ProgramManager.mqh       # 程序管理器
├── MainController.mqh       # 主控制器
├── CustomProgramA.mqh       # 範例程序類
├── ProgramExample.mqh       # 使用範例
├── TestEA.mq4              # 測試 EA
└── README.md               # 說明文檔
```

## 🎯 設計原則

1. **模組化** - 所有功能封裝為獨立模組
2. **單一職責** - 每個模組只負責一個特定功能
3. **開放封閉** - 對擴展開放，對修改封閉
4. **依賴倒置** - 依賴抽象而非具體實現
5. **介面隔離** - 提供最小必要介面

## 🔍 範例程序

### 日誌記錄程序

```mql4
class LoggerProgram : public ProgramBase
{
public:
    LoggerProgram() : ProgramBase("Logger", PROGRAM_TYPE_TICK, ProgramManager::GetInstance()) {}
    
    virtual bool Execute() override
    {
        static datetime lastLog = 0;
        if(TimeCurrent() - lastLog > 60)
        {
            LogInfo("系統運行正常 - " + TimeToString(TimeCurrent()));
            lastLog = TimeCurrent();
        }
        return true;
    }
};
```

### 狀態監控程序

```mql4
class StatusMonitorProgram : public ProgramBase
{
public:
    StatusMonitorProgram() : ProgramBase("StatusMonitor", PROGRAM_TYPE_TICK, ProgramManager::GetInstance()) {}
    
    virtual bool Execute() override
    {
        LogInfo("帳戶餘額: " + DoubleToString(AccountBalance(), 2));
        return true;
    }
};
```

## 🧪 測試

運行 `TestEA.mq4` 來測試系統功能：

1. 編譯並運行 TestEA
2. 觀察日誌輸出
3. 按 'S' 鍵查看系統狀態
4. 檢查程序執行統計

## 📈 擴展指南

### 創建新的程序類型

1. 繼承 `ProgramBase` 類
2. 實現 `Execute()` 方法
3. 選擇適當的 `ProgramType`
4. 註冊到 `ProgramManager`

### 自定義主控制器

1. 繼承 `MainController` 類
2. 重寫 `CreateDefaultPrograms()` 方法
3. 添加自定義初始化邏輯

## 🔧 故障排除

### 常見問題

1. **程序未執行** - 檢查是否正確註冊到管理器
2. **編譯錯誤** - 確保包含所有必要的頭文件
3. **記憶體洩漏** - 確保正確刪除程序實例

### 調試技巧

1. 啟用調試模式查看詳細日誌
2. 使用 `PrintSystemStatus()` 檢查系統狀態
3. 檢查程序執行統計

## 📝 版本歷史

- **v1.0** - 初始版本，實現基本模組化架構
- 基於 mermaid 類圖設計
- 支援三階段程序執行
- 完整的錯誤處理和日誌記錄

## 🤝 貢獻

歡迎提交問題報告和功能請求。請遵循以下原則：

1. 保持代碼簡潔清晰
2. 添加適當的中文註解
3. 遵循現有的編碼風格
4. 提供測試範例

## 📄 授權

本專案採用 MIT 授權條款。
