//+------------------------------------------------------------------+
//|                                              TestFramework.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

//+------------------------------------------------------------------+
//| 測試結果類別 - 記錄單個測試的結果                                 |
//+------------------------------------------------------------------+
class TestResult
{
private:
    bool m_passed;           // 測試是否通過
    string m_testName;       // 測試名稱
    string m_message;        // 測試消息

public:
    // 構造函數
    TestResult(string testName, bool passed, string message = "")
        : m_testName(testName), m_passed(passed), m_message(message) {}

    // 獲取測試名稱
    string GetTestName() const { return m_testName; }
    
    // 檢查測試是否通過
    bool IsPassed() const { return m_passed; }
    
    // 獲取測試消息
    string GetMessage() const { return m_message; }
};

//+------------------------------------------------------------------+
//| 斷言類別 - 提供各種測試斷言方法                                   |
//+------------------------------------------------------------------+
class Assert
{
public:
    // 斷言為真
    static TestResult* AssertTrue(string testName, bool condition, string message = "")
    {
        if(!condition && message == "")
            message = "期望條件為真，但實際為假";
        return new TestResult(testName, condition, message);
    }

    // 斷言為假
    static TestResult* AssertFalse(string testName, bool condition, string message = "")
    {
        if(condition && message == "")
            message = "期望條件為假，但實際為真";
        return new TestResult(testName, !condition, message);
    }

    // 斷言相等（字符串）
    static TestResult* AssertEquals(string testName, string expected, string actual, string message = "")
    {
        bool isEqual = (expected == actual);
        if(!isEqual && message == "")
            message = StringFormat("期望值: '%s', 實際值: '%s'", expected, actual);
        return new TestResult(testName, isEqual, message);
    }

    // 斷言相等（整數）
    static TestResult* AssertEquals(string testName, int expected, int actual, string message = "")
    {
        bool isEqual = (expected == actual);
        if(!isEqual && message == "")
            message = StringFormat("期望值: %d, 實際值: %d", expected, actual);
        return new TestResult(testName, isEqual, message);
    }

    // 斷言相等（布爾）
    static TestResult* AssertEquals(string testName, bool expected, bool actual, string message = "")
    {
        bool isEqual = (expected == actual);
        if(!isEqual && message == "")
            message = StringFormat("期望值: %s, 實際值: %s", 
                                 expected ? "true" : "false", 
                                 actual ? "true" : "false");
        return new TestResult(testName, isEqual, message);
    }

    // 斷言不為空
    static TestResult* AssertNotNull(string testName, void* pointer, string message = "")
    {
        bool isNotNull = (pointer != NULL);
        if(!isNotNull && message == "")
            message = "期望指針不為空，但實際為空";
        return new TestResult(testName, isNotNull, message);
    }

    // 斷言為空
    static TestResult* AssertNull(string testName, void* pointer, string message = "")
    {
        bool isNull = (pointer == NULL);
        if(!isNull && message == "")
            message = "期望指針為空，但實際不為空";
        return new TestResult(testName, isNull, message);
    }

    // 斷言包含字符串
    static TestResult* AssertContains(string testName, string haystack, string needle, string message = "")
    {
        bool contains = (StringFind(haystack, needle) >= 0);
        if(!contains && message == "")
            message = StringFormat("期望 '%s' 包含 '%s'", haystack, needle);
        return new TestResult(testName, contains, message);
    }

    // 斷言不包含字符串
    static TestResult* AssertNotContains(string testName, string haystack, string needle, string message = "")
    {
        bool notContains = (StringFind(haystack, needle) < 0);
        if(!notContains && message == "")
            message = StringFormat("期望 '%s' 不包含 '%s'", haystack, needle);
        return new TestResult(testName, notContains, message);
    }
};

//+------------------------------------------------------------------+
//| 測試基類 - 所有測試類別的基類                                     |
//+------------------------------------------------------------------+
class TestCase
{
protected:
    string m_testClassName;  // 測試類別名稱

public:
    // 構造函數
    TestCase(string className) : m_testClassName(className) {}

    // 虛析構函數
    virtual ~TestCase() {}

    // 獲取測試類別名稱
    string GetClassName() const { return m_testClassName; }

    // 運行所有測試（由子類實現）
    virtual void RunTests() = 0;

    // 設置方法（在每個測試前調用）
    virtual void SetUp() {}

    // 清理方法（在每個測試後調用）
    virtual void TearDown() {}
};

//+------------------------------------------------------------------+
//| 測試運行器 - 管理和執行所有測試                                   |
//+------------------------------------------------------------------+
class TestRunner
{
private:
    int m_totalTests;        // 總測試數
    int m_passedTests;       // 通過的測試數
    int m_failedTests;       // 失敗的測試數

public:
    // 構造函數
    TestRunner() : m_totalTests(0), m_passedTests(0), m_failedTests(0) {}

    // 記錄測試結果
    virtual void RecordResult(TestResult* result)
    {
        if(result == NULL) return;

        m_totalTests++;

        if(result.IsPassed())
        {
            m_passedTests++;
            Print(StringFormat("[通過] %s", result.GetTestName()));
        }
        else
        {
            m_failedTests++;
            Print(StringFormat("[失敗] %s: %s", result.GetTestName(), result.GetMessage()));
        }

        delete result;
    }

    // 運行測試類別
    virtual void RunTestCase(TestCase* testCase)
    {
        if(testCase == NULL) return;

        Print(StringFormat("=== 開始執行測試類別: %s ===", testCase.GetClassName()));
        testCase.RunTests();
        Print(StringFormat("=== 完成測試類別: %s ===", testCase.GetClassName()));
    }

    // 顯示測試摘要
    virtual void ShowSummary()
    {
        Print("=== 測試摘要 ===");
        Print(StringFormat("總測試數: %d", m_totalTests));
        Print(StringFormat("通過: %d", m_passedTests));
        Print(StringFormat("失敗: %d", m_failedTests));
        Print(StringFormat("成功率: %.2f%%", 
              m_totalTests > 0 ? (double)m_passedTests / m_totalTests * 100.0 : 0.0));
    }

    // 獲取測試統計
    int GetTotalTests() const { return m_totalTests; }
    int GetPassedTests() const { return m_passedTests; }
    int GetFailedTests() const { return m_failedTests; }
    bool AllTestsPassed() const { return m_failedTests == 0 && m_totalTests > 0; }
};
