//+------------------------------------------------------------------+
//|                                              PipelineGroup.mqh |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "CompositePipeline.mqh"

//+------------------------------------------------------------------+
//| 流水線組類                                                       |
//+------------------------------------------------------------------+
class PipelineGroup
{
private:
    string m_name;                      // 組名稱
    string m_type;                      // 組類型
    string m_description;               // 組描述
    Vector<CompositePipeline*> m_pipelines; // 流水線向量
    bool m_owned;                        // 是否擁有流水線
    bool m_isEnabled;                   // 是否啟用
    bool m_executed;                    // 是否已執行
    ENUM_TRADING_EVENT m_eventType;     // 關聯的事件類型
    PipelineResult* m_last_result;       // 執行結果

public:
    // 構造函數
    PipelineGroup(string name, 
                 string description = "",
                 ENUM_TRADING_EVENT eventType = TRADING_TICK,
                 bool owned = false,
                 string type = "PipelineGroup")
        : m_name(name),
          m_type(type),
          m_description(description),
          m_executed(false),
          m_isEnabled(true),
          m_eventType(eventType),
          m_pipelines(owned),
          m_owned(owned),
          m_last_result(new PipelineResult(false, "尚未執行", name, ERROR_LEVEL_INFO))
    {
    }
    
    // 析構函數
    ~PipelineGroup()
    {
        Clear();
    }
    
    // 添加流水線
    bool AddPipeline(CompositePipeline* pipeline)
    {
        if(pipeline == NULL) {
            m_last_result = new PipelineResult(false, "流水線為空", m_name, ERROR_LEVEL_ERROR);
            return false;
        }
        bool result = m_pipelines.add(pipeline);
        m_last_result = new PipelineResult(result, result 
                                                    ? "成功添加流水線" 
                                                    : "添加流水線失敗", m_name, 
                                                    result ? ERROR_LEVEL_INFO : ERROR_LEVEL_ERROR);
        return result;
    }
    
    // 移除流水線
    bool RemovePipeline(CompositePipeline* pipeline)
    {
        if(pipeline == NULL) {
            m_last_result = new PipelineResult(false, "流水線為空", m_name, ERROR_LEVEL_ERROR);
            return false;
        }
        bool result = m_pipelines.remove(pipeline);
        m_last_result = new PipelineResult(result, result 
                                                    ? "成功移除流水線" 
                                                    : "移除流水線失敗", m_name,
                                                    result ? ERROR_LEVEL_INFO : ERROR_LEVEL_ERROR);
        return result;
    }
    
    // 按名稱查找流水線
    CompositePipeline* FindPipelineByName(string name, CompositePipeline* parent = NULL)
    {
        foreachv(CompositePipeline*, pipeline, m_pipelines)
        {
            if(pipeline.GetName() == name)
            {
                m_last_result = new PipelineResult(true, "成功找到流水線", m_name, ERROR_LEVEL_INFO);
                return pipeline;
            }
        }
        m_last_result = new PipelineResult(false, 
            StringFormat("未找到名稱為 '%s' 的流水線", name), 
            m_name,
            ERROR_LEVEL_WARNING);
        return parent;
    }
    
    // 執行組中所有流水線
    void ExecuteAll()
    {
        if(!m_isEnabled) {
            m_last_result = new PipelineResult(false, "流水線組已禁用，跳過執行", m_name, ERROR_LEVEL_WARNING);
            return;
        }
        
        foreachv(CompositePipeline*, pipeline, m_pipelines)
        {
            pipeline.Execute();
        }
        m_executed = true;
    }
    
    // 還原組中所有流水線
    void RestoreAll()
    {
        foreachv(CompositePipeline*, pipeline, m_pipelines)
        {
            pipeline.Restore();
        }
        m_executed = false;
    }
    
    // 清空組
    void Clear()
    {
        m_pipelines.clear();
    }
    
    // 獲取流水線數量
    int GetPipelineCount() const
    {
        return m_pipelines.size();
    }
    
    // 獲取組名稱
    string GetName() const
    {
        return m_name;
    }

    // 獲取組類型
    string GetType() const
    {
        return m_type;
    }
    
    // 獲取組描述
    string GetDescription() const
    {
        return m_description;
    }

    // 獲取執行結果
    bool IsExecuted() const
    {
        return m_executed;
    }
    
    // 啟用/禁用組
    void SetEnabled(bool enabled)
    {
        m_isEnabled = enabled;
    }
    
    bool IsEnabled() const
    {
        return m_isEnabled;
    }
    
    // 獲取事件類型
    ENUM_TRADING_EVENT GetEventType() const
    {
        return m_eventType;
    }
    
    // 獲取組狀態信息
    string GetStatusInfo() const
    {
        string info = StringFormat("組名稱: %s\n類型: %s\n描述: %s\n狀態: %s\n流水線數量: %d",
            m_name, m_type, m_description,
            m_isEnabled ? "啟用" : "禁用",
            m_pipelines.size());
        return info;
    }
    
    // 按索引獲取流水線
    CompositePipeline* GetPipeline(int index, CompositePipeline* parent = NULL)
    {
        if(index < 0 || index >= m_pipelines.size()) {
            m_last_result = new PipelineResult(false, "索引超出範圍", m_name, ERROR_LEVEL_ERROR);
            return parent;
        }
        m_last_result = new PipelineResult(true, "成功獲取流水線", m_name, ERROR_LEVEL_INFO);
        return m_pipelines.get(index);
    }
    
    // 獲取所有流水線的名稱
    void GetPipelineNames(string &names[])
    {
        ArrayResize(names, m_pipelines.size());
        for(int i = 0; i < m_pipelines.size(); i++)
        {
            CompositePipeline* pipeline = m_pipelines.get(i);
            names[i] = pipeline != NULL ? pipeline.GetName() : "";
        }
    }
    
    // 檢查是否包含指定流水線
    bool ContainsPipeline(CompositePipeline* pipeline)
    {
        bool result = m_pipelines.contains(pipeline);
        m_last_result = new PipelineResult(result, 
            result ? "找到指定流水線" : "未找到指定流水線", 
            m_name,
            result ? ERROR_LEVEL_INFO : ERROR_LEVEL_WARNING);
        return result;
    }
    
    // 檢查是否包含指定名稱的流水線
    bool ContainsPipelineByName(string name)
    {
        bool result = (FindPipelineByName(name) != NULL);
        m_last_result = new PipelineResult(result, 
            result ? StringFormat("找到名稱為 '%s' 的流水線", name) 
                   : StringFormat("未找到名稱為 '%s' 的流水線", name), 
            m_name,
            result ? ERROR_LEVEL_INFO : ERROR_LEVEL_WARNING);
        return result;
    }
    
    // 獲取最後執行結果
    PipelineResult* GetLastResult() const
    {
        return m_last_result;
    }
};
