//+------------------------------------------------------------------+
//|                                      TestCompositePipeline.mqh |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../TestFramework.mqh"
#include "../../CompositePipeline.mqh"
#include "../../TradingPipeline.mqh"

// 模擬 TradingPipeline 以便測試
class MockTradingPipeline : public TradingPipeline
{
public:
    bool executed;
    string lastError;

    MockTradingPipeline(string name) : TradingPipeline(name), executed(false), lastError("") {}

protected:
    virtual void Main() override
    {
        executed = true;
        // 模擬一些操作
    }
};

//+------------------------------------------------------------------+
//| TestCompositePipeline 測試類                                     |
//+------------------------------------------------------------------+
class TestCompositePipeline : public TestCase
{
public:
    TestCompositePipeline() : TestCase("TestCompositePipeline") {}

    // 運行所有測試
    void RunTests() override
    {
        // 使用 TestRunner 執行測試
        TestRunner runner;
        runner.RunTestCase(GetPointer(this));
    }

protected:
    // 測試 AddPipeline 方法
    void TestAddPipeline()
    {
        Print("\n--- 測試 AddPipeline ---");
        CompositePipeline* composite = new CompositePipeline("TestAdd");
        MockTradingPipeline* pipeline1 = new MockTradingPipeline("Pipeline1");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("Pipeline2");

        // 測試成功添加
        Assert::AssertTrue("TestAddPipeline", composite.AddPipeline(pipeline1), "應能成功添加第一個流水線");
        Assert::AssertEquals("TestAddPipeline", 1, composite.GetPipelineCount(), "流水線數量應為 1");

        // 測試添加第二個
        Assert::AssertTrue("TestAddPipeline", composite.AddPipeline(pipeline2), "應能成功添加第二個流水線");
        Assert::AssertEquals("TestAddPipeline", 2, composite.GetPipelineCount(), "流水線數量應為 2");

        // 測試添加空流水線
        Assert::AssertFalse("TestAddPipeline", composite.AddPipeline(NULL), "不應能添加空流水線");

        // 測試超過最大數量限制
        CompositePipeline* limitedComposite = new CompositePipeline("LimitedTest", false, 1);
        MockTradingPipeline* limitedPipeline = new MockTradingPipeline("LimitedPipeline");
        Assert::AssertTrue("TestAddPipeline", limitedComposite.AddPipeline(limitedPipeline), "應能添加一個流水線到有限容量的複合流水線");
        Assert::AssertFalse("TestAddPipeline", limitedComposite.AddPipeline(pipeline1), "不應能添加超過最大數量的流水線");

        delete composite;
        delete pipeline1;
        delete pipeline2;
        delete limitedComposite;
        delete limitedPipeline;
    }

    // 測試 RemovePipeline 方法
    void TestRemovePipeline()
    {
        Print("\n--- 測試 RemovePipeline ---");
        CompositePipeline* composite = new CompositePipeline("TestRemove");
        MockTradingPipeline* pipeline1 = new MockTradingPipeline("Pipeline1");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("Pipeline2");

        composite.AddPipeline(pipeline1);
        composite.AddPipeline(pipeline2);

        // 測試成功移除
        Assert::AssertTrue("TestRemovePipeline", composite.RemovePipeline(pipeline1), "應能成功移除第一個流水線");
        Assert::AssertEquals("TestRemovePipeline", 1, composite.GetPipelineCount(), "流水線數量應為 1");

        // 測試移除不存在的流水線
        Assert::AssertFalse("TestRemovePipeline", composite.RemovePipeline(new MockTradingPipeline("NonExistent")), "不應能移除不存在的流水線");

        // 測試移除空流水線
        Assert::AssertFalse("TestRemovePipeline", composite.RemovePipeline(NULL), "不應能移除空流水線");

        delete composite;
        delete pipeline1;
        delete pipeline2;
    }

    // 測試 RemovePipelineByName 方法
    void TestRemovePipelineByName()
    {
        Print("\n--- 測試 RemovePipelineByName ---");
        CompositePipeline* composite = new CompositePipeline("TestRemoveByName");
        MockTradingPipeline* pipeline1 = new MockTradingPipeline("Pipeline1");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("Pipeline2");

        composite.AddPipeline(pipeline1);
        composite.AddPipeline(pipeline2);

        // 測試成功移除
        Assert::AssertTrue("TestRemovePipelineByName", composite.RemovePipelineByName("Pipeline1"), "應能成功按名稱移除流水線");
        Assert::AssertEquals("TestRemovePipelineByName", 1, composite.GetPipelineCount(), "流水線數量應為 1");

        // 測試移除不存在的流水線
        Assert::AssertFalse("TestRemovePipelineByName", composite.RemovePipelineByName("NonExistent"), "不應能按名稱移除不存在的流水線");

        delete composite;
        delete pipeline1;
        delete pipeline2;
    }

    // 測試 Clear 方法
    void TestClearPipelines()
    {
        Print("\n--- 測試 ClearPipelines ---");
        CompositePipeline* composite = new CompositePipeline("TestClear");
        composite.AddPipeline(new MockTradingPipeline("Pipeline1"));
        composite.AddPipeline(new MockTradingPipeline("Pipeline2"));

        composite.Clear();
        Assert::AssertEquals("TestClearPipelines", 0, composite.GetPipelineCount(), "流水線數量應為 0");

        delete composite;
    }

    // 測試 GetPipelineCount 方法
    void TestGetPipelineCount()
    {
        Print("\n--- 測試 GetPipelineCount ---");
        CompositePipeline* composite = new CompositePipeline("TestCount");
        Assert::AssertEquals("TestGetPipelineCount", 0, composite.GetPipelineCount(), "初始流水線數量應為 0");

        composite.AddPipeline(new MockTradingPipeline("Pipeline1"));
        Assert::AssertEquals("TestGetPipelineCount", 1, composite.GetPipelineCount(), "添加後流水線數量應為 1");

        composite.AddPipeline(new MockTradingPipeline("Pipeline2"));
        Assert::AssertEquals("TestGetPipelineCount", 2, composite.GetPipelineCount(), "添加後流水線數量應為 2");

        composite.RemovePipelineByName("Pipeline1");
        Assert::AssertEquals("TestGetPipelineCount", 1, composite.GetPipelineCount(), "移除後流水線數量應為 1");

        delete composite;
    }

    // 測試 GetMaxPipelines 方法
    void TestGetMaxPipelines()
    {
        Print("\n--- 測試 GetMaxPipelines ---");
        CompositePipeline* compositeDefault = new CompositePipeline("TestMaxDefault");
        Assert::AssertEquals("TestGetMaxPipelines", 20, compositeDefault.GetMaxPipelines(), "默認最大流水線數量應為 20");

        CompositePipeline* compositeCustom = new CompositePipeline("TestMaxCustom", false, 5);
        Assert::AssertEquals("TestGetMaxPipelines", 5, compositeCustom.GetMaxPipelines(), "自定義最大流水線數量應為 5");

        delete compositeDefault;
        delete compositeCustom;
    }

    // 測試 GetPipeline 方法 (按索引)
    void TestGetPipelineByIndex()
    {
        Print("\n--- 測試 GetPipelineByIndex ---");
        CompositePipeline* composite = new CompositePipeline("TestGetByIndex");
        MockTradingPipeline* pipeline1 = new MockTradingPipeline("Pipeline1");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("Pipeline2");

        composite.AddPipeline(pipeline1);
        composite.AddPipeline(pipeline2);

        int error = 0;
        // 測試有效索引
        ITradingPipeline* retrievedPipeline = composite.GetPipeline(0, &error);
        Assert::AssertNotNull("TestGetPipelineByIndex", retrievedPipeline, "應能獲取索引 0 的流水線");
        Assert::AssertEquals("TestGetPipelineByIndex", "Pipeline1", retrievedPipeline.GetName(), "獲取的流水線名稱應為 Pipeline1");

        retrievedPipeline = composite.GetPipeline(1, &error);
        Assert::AssertNotNull("TestGetPipelineByIndex", retrievedPipeline, "應能獲取索引 1 的流水線");
        Assert::AssertEquals("TestGetPipelineByIndex", "Pipeline2", retrievedPipeline.GetName(), "獲取的流水線名稱應為 Pipeline2");

        // 測試無效索引
        Assert::AssertNull("TestGetPipelineByIndex", composite.GetPipeline(2, &error), "不應能獲取索引 2 的流水線 (超出範圍)");
        Assert::AssertNull("TestGetPipelineByIndex", composite.GetPipeline(-1, &error), "不應能獲取索引 -1 的流水線 (超出範圍)");

        delete composite;
        delete pipeline1;
        delete pipeline2;
    }

    // 測試 FindByName 方法
    void TestFindPipelineByName()
    {
        Print("\n--- 測試 FindPipelineByName ---");
        CompositePipeline* composite = new CompositePipeline("TestFindByName");
        MockTradingPipeline* pipeline1 = new MockTradingPipeline("Pipeline1");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("Pipeline2");

        composite.AddPipeline(pipeline1);
        composite.AddPipeline(pipeline2);

        int error = 0;
        // 測試找到存在的流水線
        ITradingPipeline* foundPipeline = composite.FindByName("Pipeline1", &error);
        Assert::AssertNotNull("TestFindPipelineByName", foundPipeline, "應能找到名稱為 Pipeline1 的流水線");
        Assert::AssertEquals("TestFindPipelineByName", "Pipeline1", foundPipeline.GetName(), "找到的流水線名稱應為 Pipeline1");

        // 測試找不到不存在的流水線
        Assert::AssertNull("TestFindPipelineByName", composite.FindByName("NonExistent", &error), "不應能找到名稱為 NonExistent 的流水線");

        delete composite;
        delete pipeline1;
        delete pipeline2;
    }

    // 測試 Restore 方法
    void TestRestorePipelines()
    {
        Print("\n--- 測試 RestorePipelines ---");
        CompositePipeline* composite = new CompositePipeline("TestRestore");
        MockTradingPipeline* pipeline1 = new MockTradingPipeline("Pipeline1");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("Pipeline2");

        composite.AddPipeline(pipeline1);
        composite.AddPipeline(pipeline2);

        composite.Execute(); // 執行以設置 executed 狀態
        Assert::AssertTrue("TestRestorePipelines", pipeline1.executed, "流水線1應已執行");
        Assert::AssertTrue("TestRestorePipelines", pipeline2.executed, "流水線2應已執行");

        composite.Restore();
        Assert::AssertFalse("TestRestorePipelines", pipeline1.executed, "流水線1應已重置");
        Assert::AssertFalse("TestRestorePipelines", pipeline2.executed, "流水線2應已重置");

        delete composite;
        delete pipeline1;
        delete pipeline2;
    }
    // 測試 Execute 方法
    void TestExecutePipelines()
    {
        Print("\n--- 測試 ExecutePipelines ---");
        CompositePipeline* composite = new CompositePipeline("TestExecute");
        MockTradingPipeline* pipeline1 = new MockTradingPipeline("Pipeline1");
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("Pipeline2");

        composite.AddPipeline(pipeline1);
        composite.AddPipeline(pipeline2);

        composite.Execute();
        Assert::AssertTrue("TestExecutePipelines", pipeline1.executed, "流水線1應已執行");
        Assert::AssertTrue("TestExecutePipelines", pipeline2.executed, "流水線2應已執行");

        // 再次執行，確認不會重複執行 Main
        pipeline1.executed = false; // 重置以便檢查
        pipeline2.executed = false;
        composite.Execute();
        Assert::AssertFalse("TestExecutePipelines", pipeline1.executed, "流水線1不應重複執行 Main");
        Assert::AssertFalse("TestExecutePipelines", pipeline2.executed, "流水線2不應重複執行 Main");

        delete composite;
        delete pipeline1;
        delete pipeline2;
    }


};
