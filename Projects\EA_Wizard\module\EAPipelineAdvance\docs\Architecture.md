# EAPipelineAdvance 架構文檔

## 概述

EAPipelineAdvance 是對原始 EAPipeline 模組的重構版本，採用簡化的架構設計，專注於提高代碼可讀性、降低複雜度和改善可維護性。

## 設計原則

### 1. 簡化優於複雜
- 減少不必要的抽象層
- 避免過度設計
- 使用組合替代複雜的繼承

### 2. 清晰的命名
- 使用業務相關的命名
- 避免技術術語的濫用
- 保持命名的一致性

### 3. 單一職責
- 每個類專注於單一功能
- 明確的介面定義
- 低耦合高內聚

### 4. 可測試性
- 依賴注入而非單例模式
- 清晰的輸入輸出
- 易於模擬和測試

## 架構層次

```
┌─────────────────────────────────────────┐
│              應用層 (Application)        │
│  ┌─────────────────────────────────────┐ │
│  │        EA 業務邏輯                   │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│              管理層 (Management)         │
│  ┌─────────────────────────────────────┐ │
│  │    EAPipelineAdvanceManager         │ │
│  │    - 模組初始化                      │ │
│  │    - 事件管理                        │ │
│  │    - 生命週期管理                    │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│              組合層 (Composite)          │
│  ┌─────────────────┐ ┌─────────────────┐ │
│  │ CompositePipeline│ │  PipelineGroup  │ │
│  │ - 子流水線管理   │ │  - 組管理       │ │
│  │ - 執行協調       │ │  - 事件分組     │ │
│  └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│              功能層 (Features)           │
│  ┌─────────────────┐ ┌─────────────────┐ │
│  │ LoggingPipeline │ │PipelineRegistry │ │
│  │ - 日誌記錄       │ │ - 流水線註冊    │ │
│  │ - 包裝模式       │ │ - 鍵值存儲      │ │
│  └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│              核心層 (Core)               │
│  ┌─────────────────┐ ┌─────────────────┐ │
│  │ TradingPipeline │ │PipelineExecutor │ │
│  │ - 基礎抽象       │ │ - 執行調度      │ │
│  │ - 狀態管理       │ │ - 統計收集      │ │
│  └─────────────────┘ └─────────────────┘ │
│  ┌─────────────────────────────────────┐ │
│  │           TradingEvent              │ │
│  │           - 事件定義                 │ │
│  │           - 工具函數                 │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

## 核心組件

### 1. TradingPipeline (核心抽象)

**職責**：
- 提供流水線的基礎抽象
- 管理執行狀態和結果
- 實現模板方法模式

**關鍵特性**：
- 狀態管理（PENDING, RUNNING, COMPLETED, FAILED）
- 執行統計（次數、時間）
- 錯誤處理和日誌記錄
- 啟用/禁用控制

**設計模式**：模板方法模式

```mql4
class TradingPipeline : public ITradingPipeline
{
    // 模板方法
    virtual bool Execute() override {
        // 前置檢查
        // 狀態設置
        bool result = DoExecute(); // 子類實現
        // 後置處理
        return result;
    }
    
    // 子類實現的具體邏輯
    virtual bool DoExecute() = 0;
}
```

### 2. PipelineExecutor (執行調度)

**職責**：
- 管理多個流水線的執行
- 提供重試機制
- 收集執行統計

**關鍵特性**：
- 支持順序和並行執行
- 錯誤處理策略（停止或繼續）
- 執行統計和監控
- 重試機制

### 3. CompositePipeline (組合模式)

**職責**：
- 管理子流水線的組合
- 協調子流水線的執行
- 提供統一的執行介面

**設計模式**：組合模式

```mql4
class CompositePipeline : public TradingPipeline
{
    ITradingPipeline* m_children[];
    
    virtual bool DoExecute() override {
        // 順序執行所有子流水線
        for(int i = 0; i < ArraySize(m_children); i++) {
            bool success = m_children[i].Execute();
            if(!success && m_stopOnError) {
                return false;
            }
        }
        return true;
    }
}
```

### 4. LoggingPipeline (裝飾模式)

**職責**：
- 為流水線添加日誌功能
- 包裝現有流水線
- 提供靈活的日誌配置

**設計模式**：裝飾者模式（簡化版）

```mql4
class LoggingPipeline : public TradingPipeline
{
    ITradingPipeline* m_wrappedPipeline;
    Logger* m_logger;
    
    virtual bool DoExecute() override {
        m_logger.Info("開始執行: " + m_wrappedPipeline.GetName());
        bool result = m_wrappedPipeline.Execute();
        m_logger.Info("執行完成: " + (result ? "成功" : "失敗"));
        return result;
    }
}
```

## 與原始模組的對比

### 原始 EAPipeline 的問題

1. **過度複雜的裝飾者鏈**
   ```mql4
   // 原始版本 - 複雜的裝飾者鏈
   EARegistryBase* registry = new EALogRegistry(
       new EAErrorHandlingRegistry(
           EARegistry::GetInstance(), "EARegistry"
       )
   );
   ```

2. **命名不清晰**
   - `EACompoundPipeline` vs `EALogCompoundPipeline`
   - 過多的 "EA" 前綴
   - 技術術語過多

3. **高耦合度**
   - 大量單例模式
   - 複雜的依賴關係
   - 難以測試

### EAPipelineAdvance 的改進

1. **簡化的組合**
   ```mql4
   // 新版本 - 簡化的組合
   CompositePipeline* pipeline = new CompositePipeline("MainPipeline");
   pipeline.Add(new DataFeedPipeline());
   pipeline.Add(new SignalPipeline());
   ```

2. **清晰的命名**
   - `TradingPipeline` - 明確的業務含義
   - `PipelineExecutor` - 專注執行職責
   - `CompositePipeline` - 去除冗餘前綴

3. **低耦合設計**
   - 依賴注入替代單例
   - 清晰的介面定義
   - 易於測試和模擬

## 設計模式使用

### 1. 模板方法模式
- **使用位置**：TradingPipeline
- **目的**：定義執行框架，子類實現具體邏輯
- **優勢**：統一的執行流程，靈活的業務邏輯

### 2. 組合模式
- **使用位置**：CompositePipeline
- **目的**：統一處理單個和組合流水線
- **優勢**：靈活的層次結構，統一的介面

### 3. 裝飾者模式（簡化）
- **使用位置**：LoggingPipeline
- **目的**：動態添加功能
- **優勢**：功能組合，不修改原有代碼

### 4. 工廠模式
- **使用位置**：EAPipelineAdvanceFactory
- **目的**：統一對象創建
- **優勢**：封裝創建邏輯，易於維護

### 5. 單例模式（限制使用）
- **使用位置**：僅在 EAPipelineAdvanceManager
- **目的**：全局訪問點
- **限制**：僅在必要時使用，避免過度依賴

## 錯誤處理策略

### 1. 分層錯誤處理

```
應用層：業務邏輯錯誤
    ↓
管理層：配置和初始化錯誤
    ↓
組合層：流水線組合錯誤
    ↓
功能層：功能特定錯誤
    ↓
核心層：基礎執行錯誤
```

### 2. 錯誤級別

- **INFO**：信息性消息
- **WARNING**：警告，不影響執行
- **ERROR**：錯誤，影響當前操作
- **CRITICAL**：嚴重錯誤，需要立即處理

### 3. 錯誤傳播

- 子流水線錯誤向上傳播
- 複合流水線可選擇停止或繼續
- 執行器收集所有錯誤信息

## 性能考慮

### 1. 對象重用
- 避免頻繁創建和銷毀對象
- 使用對象池（如需要）
- 重置而非重新創建

### 2. 記憶體管理
- 明確的對象生命週期
- 及時釋放資源
- 避免記憶體洩漏

### 3. 執行效率
- 條件執行避免不必要的處理
- 並行執行（如適用）
- 快速失敗策略

## 擴展性設計

### 1. 新流水線類型
- 繼承 TradingPipeline
- 實現 DoExecute 方法
- 添加特定的業務邏輯

### 2. 新功能組件
- 實現相應的介面
- 使用組合模式集成
- 保持與現有組件的兼容性

### 3. 新執行模式
- 擴展 ENUM_EXECUTION_MODE
- 在 PipelineExecutor 中實現
- 保持向後兼容性

## 測試策略

### 1. 單元測試
- 每個類的獨立測試
- 模擬依賴對象
- 覆蓋所有執行路徑

### 2. 整合測試
- 組件間的協作測試
- 端到端的執行流程
- 錯誤場景測試

### 3. 性能測試
- 執行時間測量
- 記憶體使用監控
- 壓力測試

## 最佳實踐

### 1. 設計原則
- 保持簡單
- 單一職責
- 開放封閉
- 依賴倒置

### 2. 編碼規範
- 清晰的命名
- 適當的註釋
- 一致的風格
- 錯誤處理

### 3. 維護指南
- 定期重構
- 性能監控
- 文檔更新
- 版本控制

## 未來發展

### 1. 可能的改進
- 異步執行支持
- 更豐富的監控功能
- 配置文件支持
- 插件機制

### 2. 兼容性考慮
- 向後兼容性
- 遷移工具
- 版本管理
- 文檔維護

這個重構的架構提供了一個更簡潔、更易維護的流水線處理框架，同時保持了原有的功能完整性和擴展性。
